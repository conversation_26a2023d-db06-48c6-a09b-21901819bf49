local cars = {}

-- ارجاع جميع السيارات عند الريستارت
--[[MySQL.ready(function()
    MySQL.Async.execute("UPDATE owned_vehicles SET stored=true WHERE stored=false")
end)]]

-- Make sure all Vehicles are Stored on restart
MySQL.ready(function()
	if Config.Main.ParkVehicles then
		ParkVehicles()
	else
		print('esx_advancedgarage: Parking Vehicles on restart is currently set to false.')
	end
end)

function ParkVehicles()
	MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = true WHERE `stored` = @stored', {
		['@stored'] = false
	}, function(rowsChanged)
		if rowsChanged > 0 then
			print(('esx_advancedgarage: %s vehicle(s) have been stored!'):format(rowsChanged))
		end
	end)
end

-- Add Command for Getting Properties
if Config.Main.Commands then
	ESX.RegisterCommand('getgarages', 'user', function(xPlayer, args, showError)
		xPlayer.triggerEvent('esx_advancedgarage:getPropertiesC')
	end, true, {help = 'Get Private Garages', validate = false})
end

-- Add Print Command for Getting Properties
RegisterNetEvent('esx_advancedgarage:printGetProperties')
AddEventHandler('esx_advancedgarage:printGetProperties', function()
	print('Getting Properties')
end)

-- Get Owned Properties
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedProperties', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	local properties = {}

	MySQL.Async.fetchAll('SELECT * FROM owned_properties WHERE owner = @owner', {
		['@owner'] = xPlayer.identifier
	}, function(data)
		for _,v in pairs(data) do
			table.insert(properties, v.name)
		end
		cb(properties)
	end)
end)

-- Start of Ambulance Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedAmbulanceCars', function(source, cb)
	local ownedAmbulanceCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'ambulance'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAmbulanceCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedAmbulanceCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'ambulance',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAmbulanceCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedAmbulanceCars)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOwnedAmbulanceAircrafts', function(source, cb)
	local ownedAmbulanceAircrafts = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.AdvVehShop then
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'ambulance'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAmbulanceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedAmbulanceAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'ambulance',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAmbulanceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedAmbulanceAircrafts)
			end)
		end
	else
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'ambulance'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAmbulanceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedAmbulanceAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'ambulance',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAmbulanceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedAmbulanceAircrafts)
			end)
		end
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedAmbulanceCars', function(source, cb)
	local ownedAmbulanceCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = xPlayer.identifier,
		['@job'] = 'ambulance',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedAmbulanceCars, {vehicle = vehicle, name = v.name})
		end
		cb(ownedAmbulanceCars)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyAmbulance', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Ambulance.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payAmbulance')
AddEventHandler('esx_advancedgarage:payAmbulance', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Ambulance.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Ambulance.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Ambulance.PoundP)
		end)
	end
end)
-- End of Ambulance Code

-- Start of Police Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedPoliceCars', function(source, cb)
	local ownedPoliceCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'police'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedPoliceCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedPoliceCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'police',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedPoliceCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedPoliceCars)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOwnedPoliceAircrafts', function(source, cb)
	local ownedPoliceAircrafts = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.AdvVehShop then
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'police'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedPoliceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedPoliceAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'police',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedPoliceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedPoliceAircrafts)
			end)
		end
	else
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'police'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedPoliceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedPoliceAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'police',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedPoliceAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedPoliceAircrafts)
			end)
		end
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedPoliceCars', function(source, cb)
	local ownedPoliceCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = xPlayer.identifier,
		['@job'] = 'police',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedPoliceCars, {vehicle = vehicle, name = v.name})
		end
		cb(ownedPoliceCars)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyPolice', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Police.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payPolice')
AddEventHandler('esx_advancedgarage:payPolice', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Police.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Police.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Police.PoundP)
		end)
	end
end)
-- End of Police Code

-------------------------------------------------------
-------------------------------------------------------
------------------------------------------------------
-- Start of agent Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedagentCars', function(source, cb)
	local ownedagentCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'agent'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedagentCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedagentCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'agent',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedagentCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedagentCars)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOwnedagentAircrafts', function(source, cb)
	local ownedagentAircrafts = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.AdvVehShop then
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'agent'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedagentAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedagentAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'agent',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedagentAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedagentAircrafts)
			end)
		end
	else
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'agent'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedagentAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedagentAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'agent',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedagentAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedagentAircrafts)
			end)
		end
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedagentCars', function(source, cb)
	local ownedagentCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = xPlayer.identifier,
		['@job'] = 'agent',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedagentCars, {vehicle = vehicle, name = v.name})
		end
		cb(ownedagentCars)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyagent', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.agent.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payagent')
AddEventHandler('esx_advancedgarage:payagent', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.agent.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.agent.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.agent.PoundP)
		end)
	end
end)
-- End of agent Code

-------------------------------------------------------
-------------------------------------------------------
------------------------------------------------------
-- Start of Admin Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedAdminCars', function(source, cb)
	local ownedAdminCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'admin'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAdminCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedAdminCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'admin',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAdminCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedAdminCars)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOwnedAdminAircrafts', function(source, cb)
	local ownedAdminAircrafts = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.AdvVehShop then
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'admin'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAdminAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedAdminAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'aircraft',
				['@job'] = 'admin',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAdminAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'aircraft'})
				end
				cb(ownedAdminAircrafts)
			end)
		end
	else
		if Config.Main.ShowVehLoc then
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'admin'
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAdminAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedAdminAircrafts)
			end)
		else
			MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
				['@owner'] = xPlayer.identifier,
				['@Type'] = 'helicopter',
				['@job'] = 'admin',
				['@stored'] = true
			}, function(data)
				for _,v in pairs(data) do
					local vehicle = json.decode(v.vehicle)
					table.insert(ownedAdminAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, vtype = 'helicopter'})
				end
				cb(ownedAdminAircrafts)
			end)
		end
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedAdminCars', function(source, cb)
	local ownedAdminCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = xPlayer.identifier,
		['@job'] = 'admin',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedAdminCars, vehicle)
		end
		cb(ownedAdminCars)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyAdmin', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Admin.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payAdmin')
AddEventHandler('esx_advancedgarage:payAdmin', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Admin.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Admin.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Admin.PoundP)
		end)
	end
end)
-- End of Admin Code

-- Start of Mechanic Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedMechanicCars', function(source, cb)
	local ownedMechanicCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'mechanic'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedMechanicCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedMechanicCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'mechanic',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedMechanicCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedMechanicCars)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedMechanicCars', function(source, cb)
	local ownedMechanicCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = xPlayer.identifier,
		['@job'] = 'mechanic',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedMechanicCars, {vehicle = vehicle, name = v.name})
		end
		cb(ownedMechanicCars)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyMechanic', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Mechanic.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payMechanic')
AddEventHandler('esx_advancedgarage:payMechanic', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Mechanic.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Mechanic.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Mechanic.PoundP)
		end)
	end
end)
-- End of Mechanic Code

-- Start of Aircraft Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedAircrafts', function(source, cb)
	local ownedAircrafts = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'aircraft',
			['@job'] = 'civ'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedAircrafts)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'aircraft',
			['@job'] = 'civ',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAircrafts, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedAircrafts)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedAircrafts', function(source, cb)
	local ownedAircrafts = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
		['@owner'] = xPlayer.identifier,
		['@Type'] = 'aircraft',
		['@job'] = 'civ',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedAircrafts, vehicle)
		end
		cb(ownedAircrafts)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyAircrafts', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Aircrafts.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payAircraft')
AddEventHandler('esx_advancedgarage:payAircraft', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Aircrafts.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Aircrafts.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Aircrafts.PoundP)
		end)
	end
end)
-- End of Aircraft Code

-- Start of Helicopter Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedHelicopters', function(source, cb)
	local ownedHelicopters = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'helicopter',
			['@job'] = 'civ'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedHelicopters, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedHelicopters)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'helicopter',
			['@job'] = 'civ',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedHelicopters, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedHelicopters)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedHelicopters', function(source, cb)
	local ownedHelicopters = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
		['@owner'] = xPlayer.identifier,
		['@Type'] = 'helicopter',
		['@job'] = 'civ',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedHelicopters, vehicle)
		end
		cb(ownedHelicopters)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyHelicopters', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Helicopters.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payHelicopter')
AddEventHandler('esx_advancedgarage:payHelicopter', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Helicopters.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Helicopters.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Helicopters.PoundP)
		end)
	end
end)
-- End of Helicopter Code

-- Start of Truck Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedTrucks', function(source, cb)
	local ownedTrucks = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'truck',
			['@job'] = 'civ'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedTrucks, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedTrucks)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'truck',
			['@job'] = 'civ',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedTrucks, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedTrucks)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedTrucks', function(source, cb)
	local ownedTrucks = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
		['@owner'] = xPlayer.identifier,
		['@Type'] = 'truck',
		['@job'] = 'civ',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedTrucks, {vehicle = vehicle, name = v.name})
		end
		cb(ownedTrucks)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyTrucks', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Trucks.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payTruck')
AddEventHandler('esx_advancedgarage:payTruck', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Trucks.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Trucks.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Trucks.PoundP)
		end)
	end
end)
-- End of Truck Code

-- Start of Boat Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedBoats', function(source, cb)
	local ownedBoats = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'boat',
			['@job'] = 'civ'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedBoats, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedBoats)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'boat',
			['@job'] = 'civ',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedBoats, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedBoats)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedBoats', function(source, cb)
	local ownedBoats = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
		['@owner'] = xPlayer.identifier,
		['@Type'] = 'boat',
		['@job'] = 'civ',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedBoats, vehicle)
		end
		cb(ownedBoats)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyBoats', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= Config.Boats.PoundP then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:payBoat')
AddEventHandler('esx_advancedgarage:payBoat', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Boats.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Boats.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Boats.PoundP)
		end)
	end
end)
-- End of Boat Code

-- Start of Car Code
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedCars', function(source, cb)
	local ownedCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.Main.ShowVehLoc then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'civ'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
			['@owner'] = xPlayer.identifier,
			['@Type'] = 'car',
			['@job'] = 'civ',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate})
			end
			cb(ownedCars)
		end)
	end
end)

ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedCars', function(source, cb)
	local ownedCars = {}
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
		['@owner'] = xPlayer.identifier,
		['@Type'] = 'car',
		['@job'] = 'civ',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			-- local name = v.name
			-- local plate = v.plate
			print(vehicle.plate)
			table.insert(ownedCars, {vehicle = vehicle, name = v.name, stored = v.stored, plate = v.plate, model = vehicle.model})
		end
		cb(ownedCars)
	end)
end)

ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyCars2', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.getMoney() >= 1500 then
		cb(true)
	else
		cb(false)
	end
end)

RegisterNetEvent('esx_advancedgarage:removeMoney')
AddEventHandler('esx_advancedgarage:removeMoney', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.Cars.PoundP)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.Cars.PoundP)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(Config.Cars.PoundP)
		end)
	end
end)
-- End of Car Code

-- Store Vehicles
ESX.RegisterServerCallback('esx_advancedgarage:storeVehicle', function (source, cb, vehicleProps)
	local ownedCars = {}
	local vehplate = vehicleProps.plate:match("^%s*(.-)%s*$")
	local vehiclemodel = vehicleProps.model
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND @plate = plate', {
		['@owner'] = xPlayer.identifier,
		['@plate'] = vehicleProps.plate
	}, function (result)
		if result[1] ~= nil then
			local originalvehprops = json.decode(result[1].vehicle)
			if originalvehprops.model == vehiclemodel then
				-- MySQL.Async.execute('UPDATE owned_vehicles SET vehicle = @vehicle WHERE owner = @owner AND plate = @plate', {
				-- 	['@owner'] = xPlayer.identifier,
				-- 	['@vehicle'] = json.encode(vehicleProps),
				-- 	['@plate'] = vehicleProps.plate
				-- }, function (rowsChanged)
				-- 	if rowsChanged == 0 then
				-- 		print(('esx_advancedgarage: %s attempted to store an vehicle they don\'t own!'):format(xPlayer.identifier))
				-- 	end
				-- 	cb(true)
				-- end)
				cb(true)
			else
				if Config.Main.KickCheaters then
					if Config.Main.CustomKickMsg then
						print(('esx_advancedgarage: %s attempted to Cheat! Tried Storing: %s | Original Vehicle: %s '):format(xPlayer.identifier, vehiclemodel, originalvehprops.model))

						DropPlayer(source, _U('custom_kick'))
						cb(false)
					else
						print(('esx_advancedgarage: %s attempted to Cheat! Tried Storing: %s | Original Vehicle: %s '):format(xPlayer.identifier, vehiclemodel, originalvehprops.model))

						DropPlayer(source, 'You have been Kicked from the Server for Possible Garage Cheating!!!')
						cb(false)
					end
				else
					print(('esx_advancedgarage: %s attempted to Cheat! Tried Storing: %s | Original Vehicle: %s '):format(xPlayer.identifier, vehiclemodel, originalvehprops.model))
					cb(false)
				end
			end
		else
			print(('esx_advancedgarage: %s attempted to store an vehicle they don\'t own!'):format(xPlayer.identifier))
			cb(false)
		end
	end)
end)

-- Pay to Return Broken Vehicles
RegisterNetEvent('esx_advancedgarage:payhealth')
AddEventHandler('esx_advancedgarage:payhealth', function(price)
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(price)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. price)

	if Config.Main.GiveSocMoney then
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
			account.addMoney(price)
		end)
	end
end)

-- Modify State of Vehicles
RegisterNetEvent('esx_advancedgarage:setVehicleState')
AddEventHandler('esx_advancedgarage:setVehicleState', function(plate, state, vehicle, healt)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)

	if state then
		MySQL.Async.fetchAll('SELECT vehicle FROM owned_vehicles WHERE plate = @plate', {
			['@plate'] = plate,
		}, function(data)
			if data[1] then
				MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = @stored, `vehicle` = @vehicle WHERE plate = @plate', {
					['@stored'] = true,
					['@vehicle'] = json.encode(vehicle),
					['@plate'] = plate,
				}, function(rowsChanged)
					if rowsChanged == 0 then
						print(('esx_advancedgarage: %s exploited the garage!'):format(xPlayer.identifier))
					end
				end)
			end
		end)
	else
		MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = @stored WHERE plate = @plate', {
			['@stored'] = false,
			['@plate'] = plate,
		}, function(rowsChanged)
			if rowsChanged == 0 then
				print(('esx_advancedgarage: %s exploited the garage!'):format(xPlayer.identifier))
			end
		end)
	end
end)


-- RegisterNetEvent("wais:sendEngineHealthS", function(plate, health)
-- 	if cars[tostring(plate)] == nil then
-- 		cars[tostring(plate)] = 1000.0
-- 	else
-- 		cars[tostring(plate)] = health
-- 	end
-- 	print('Araç tamir edildi:', cars[tostring(plate)])
-- end)