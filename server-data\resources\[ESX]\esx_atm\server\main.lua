RegisterServerEvent('esx_atm:deposit')
AddEventHandler('esx_atm:deposit', function(amount)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)
	local user = xPlayer.getName()
    local rpname = user
	amount = tonumber(amount)
	
	-- discord log
	local embeds_deposit = {
		{
			["title"]= "**إيداع** فلوس بالصرافة",
			["type"]="rich",
            ["description"] = "`["..user.."]` [steam:`"..GetPlayerName(source).."`] [`"..GetPlayerIdentifiers(source)[1].."`] [`"..GetPlayerIdentifiers(source)[5].."`] [id:`:"..source.."`] \n `"..amount.."` : مبلغ الايداع",
			["color"] = "********",
			["footer"]=  { ["text"]= "إيداع فلوس بالصرافة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	} --]]

	if not tonumber(amount) then return end
	amount = ESX.Math.Round(amount)

	if amount == nil or amount <= 0 or amount > xPlayer.getMoney() then
		TriggerClientEvent('esx:showNotification', _source, _U('invalid_amount'))
	else
		xPlayer.removeMoney(amount)
		xPlayer.addAccountMoney('bank', amount)
		TriggerClientEvent('esx:showNotification', _source, _U('deposit_money', amount))
		PerformHttpRequest("https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj", function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_deposit}), { ['Content-Type'] = 'application/json' }) -- discord log
	end
end)

RegisterServerEvent('esx_atm:withdraw')
AddEventHandler('esx_atm:withdraw', function(amount)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)
	local user = xPlayer.getName()
    local rpname = user
	amount = tonumber(amount)
	local accountMoney = xPlayer.getAccount('bank').money
	-- discord log
	local embeds_withdraw = {
		{
			["title"]= " **سحب** فلوس من الصرافة",
			["type"]="rich",
            ["description"] = "`["..user.."]` [steam:`"..GetPlayerName(source).."`] [`"..GetPlayerIdentifiers(source)[1].."`] [`"..GetPlayerIdentifiers(source)[5].."`] [id:`:"..source.."`] \n`"..amount.."` : مبلغ السحب",
			["color"] = "3066993",
			["footer"]=  { ["text"]= "سحب فلوس من الصرافة", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	} --]]

	if not tonumber(amount) then return end
	amount = ESX.Math.Round(amount)

	if amount == nil or amount <= 0 or amount > accountMoney then
		TriggerClientEvent('esx:showNotification', _source, _U('invalid_amount'))
	else
		xPlayer.removeAccountMoney('bank', amount)
		xPlayer.addMoney(amount)
		TriggerClientEvent('esx:showNotification', _source, _U('withdraw_money', amount))
		PerformHttpRequest("https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj", function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_withdraw}), { ['Content-Type'] = 'application/json' }) -- discord log
	end
end)
