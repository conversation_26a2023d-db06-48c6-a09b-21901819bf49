local seatsTaken = {} -- esx_sit
local currentPort = 1
local playersOnline = 0

AddEventHandler('playerConnecting', function(name, x3bs, Panells) -- https://cdn.discordapp.com/attachments/798515212361138197/840846216384479242/fivem.png
   if Config.EnableConnectOptions then
   Panells.defer()
   Wait(1000)
   Panells.presentCard([==[{
     "type":"AdaptiveCard",
     "$schema":"http://adaptivecards.io/schemas/adaptive-card.json",
     "version":"1.2",
     "body":[
      {
         "type": "Image",
         "url": "https://media.discordapp.net/attachments/906129273851613184/919052473828790342/joinpic.png",
         "horizontalAlignment": "Center",
         "width": "300px", 
         "height": "300px"
     },
        {
           "type":"ColumnSet",
           "columns":[
              {
                 "type":"Column",
                 "width":"stretch",
                 "items":[
                    {
                       "type":"TextBlock",
                       "text":"متجر نجم",
                       "wrap":true,

                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"Image",
                       "url":"https://cdn.discordapp.com/attachments/798515212361138197/840846217574350868/store.png",
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"TextBlock",
                       "text":"Zahya Store",
                       "wrap":true,
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"ActionSet",
                       "actions":[
                          {
                             "type":"Action.OpenUrl",
                             "title":"دخول المتجر",
                             "url":"https://zahya.tebex.io/",
                             "iconUrl":"https://cdn.discordapp.com/attachments/798515212361138197/840846217574350868/store.png"
                          }
                       ]
                    }
                 ],
                 "separator":true,
                 "style":"warning",
                 "backgroundImage":{
                    "url":"https://cdn.discordapp.com/attachments/798515212361138197/840840161931296768/200x300.png"
                 }
              },
              {
                 "type":"Column",
                 "width":"stretch",
                 "items":[
                    {
                       "type":"TextBlock",
                       "text":"دخول السيرفر",
                       "wrap":true,
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"Image",
                       "url":"https://media.discordapp.net/attachments/906129273851613184/919050875186585680/server-logo-48x48.png",
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"TextBlock",
                       "text":"Join Server",
                       "wrap":true,
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"ActionSet",
                       "actions":[
                          {
                             "type":"Action.Submit",
                             "title":"دخول السيرفر",
                             "id":"connect",
                             "iconUrl":"https://media.discordapp.net/attachments/906129273851613184/919050875186585680/server-logo-48x48.png"
                          }
                       ]
                    }
                 ],
                 "separator":true,
                 "style":"warning",
                 "backgroundImage":{
                    "url":"https://cdn.discordapp.com/attachments/798515212361138197/840840161931296768/200x300.png"
                 }
              },
              {
                 "type":"Column",
                 "width":"stretch",
                 "items":[
                    {
                       "type":"TextBlock",
                       "text":"ديسكورد المتجر",
                       "wrap":true,
                       "color": "Dark",
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"Image",
                       "url":"https://cdn.discordapp.com/attachments/798515212361138197/840846215565410344/discord.png",
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"TextBlock",
                       "text":"Discord Server",
                       "wrap":true,
                       "horizontalAlignment":"Center"
                    },
                    {
                       "type":"ActionSet",
                       "actions":[
                          {
                             "type":"Action.OpenUrl",
                             "title":"دخول الدسكورد",
                             "url": "https://discord.gg/zahya",
                             "iconUrl":"https://cdn.discordapp.com/attachments/798515212361138197/840846215565410344/discord.png"
                          }
                       ]
                    }
                 ],
                 "separator":true,
                 "style":"warning",
                 "backgroundImage":{
                    "url":"https://cdn.discordapp.com/attachments/798515212361138197/840840161931296768/200x300.png"
                 }
              }
           ]
        }
     ]
  }]==],
     function(data, rawData)
       if (data.submitId == 'connect') then 
         clicked = true;
         Panells.done()
       end
   end)
   end
 end)

ESX.RegisterServerCallback("sistema_bebidas:validarcompra", function(source, callback) -- مكينة الكوكوكولا
	local player = ESX.GetPlayerFromId(source)

	if player then
		if player.getMoney() >= Config.colaprice then
			player.removeMoney(Config.colaprice)

			callback(true)
		else
			callback(false)
		end
	else
		callback(false)
	end
end) --]]

RegisterServerEvent('esx_tgo_watercoolers:refillThirst') -- برادة الماء
AddEventHandler('esx_tgo_watercoolers:refillThirst', function()
	TriggerClientEvent('esx_status:add', source, 'thirst', 150000)
end) --]]

RegisterServerEvent('va:getPlayerIdentifiers') -- رسائل الشات التلقائية
AddEventHandler('va:getPlayerIdentifiers', function()
    if GetPlayerIdentifiers(source) ~= nil then
        TriggerClientEvent('va:setPlayerIdentifiers', source, GetPlayerIdentifiers(source))
    end
end) -- end رسائل الشات التلقائية

RegisterServerEvent("lvc_TogDfltSrnMuted_s") -- نظام السفتي lux_vehcontrol
AddEventHandler("lvc_TogDfltSrnMuted_s", function(toggle)
	TriggerClientEvent("lvc_TogDfltSrnMuted_c", -1, source, toggle)
end)

RegisterServerEvent("lvc_SetLxSirenState_s")
AddEventHandler("lvc_SetLxSirenState_s", function(newstate)
	TriggerClientEvent("lvc_SetLxSirenState_c", -1, source, newstate)
end)

RegisterServerEvent("lvc_TogPwrcallState_s")
AddEventHandler("lvc_TogPwrcallState_s", function(toggle)
	TriggerClientEvent("lvc_TogPwrcallState_c", -1, source, toggle)
end)

RegisterServerEvent("lvc_SetAirManuState_s")
AddEventHandler("lvc_SetAirManuState_s", function(newstate)
	TriggerClientEvent("lvc_SetAirManuState_c", -1, source, newstate)
end)

RegisterServerEvent("lvc_TogIndicState_s")
AddEventHandler("lvc_TogIndicState_s", function(newstate)
	TriggerClientEvent("lvc_TogIndicState_c", -1, source, newstate)
end) -- end نظام السفتي lux_vehcontrol

RegisterServerEvent("esx_misc:afkkick:kickplayer") -- afk kick start
AddEventHandler("esx_misc:afkkick:kickplayer", function(pas)
    if pas == '9283ytheo8fgWGH2abykd2' then
	DropPlayer(source, _U('afk_kicked_message'))
	end
end)

local blockedItems = {
    [`blimp`] = true,
    [`blimp2`] = true,
    [`blimp3`] = true,
    [`frogger`] = true
}

AddEventHandler('entityCreating', function(entity)
    local model = GetEntityModel(entity)
    if blockedItems[model] then
        CancelEvent()
    end
end) -- afk kick end

RegisterServerEvent('esx_misc:tryTackle') -- ktackle شفت + G
AddEventHandler('esx_misc:tryTackle', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)
	local xPlayer = ESX.GetPlayerFromId(source)
    
	if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' or xPlayer.job.name == 'admin' then
	TriggerClientEvent('esx_misc:getTackled', targetPlayer.source, source)
	TriggerClientEvent('esx_misc:playTackle', source)
	else
	print(('esx_misc: %s attempted to tryTackle (not cop)!'):format(xPlayer.identifier))
	end
end)
RegisterServerEvent('esx_misc:onlineplayersserver') 
AddEventHandler('esx_misc:onlineplayersserver', function()
TriggerClientEvent("esx_misc:onlineplayers", source, GetPlayers())
end)



ESX.RegisterServerCallback('esx_misc:getOnlinePlayers', function(source, cb)
    local players = GetPlayers() -- دالة GetPlayers تعيد قائمة باللاعبين المتصلين
    local onlinePlayers = {}

    for _, playerId in ipairs(players) do
        local xPlayer = ESX.GetPlayerFromId(playerId)
        if xPlayer then
            table.insert(onlinePlayers, {
                id = playerId,
                name = xPlayer.getName(),
                job = xPlayer.getJob().name
            })
        end
    end

    cb(onlinePlayers) -- إعادة البيانات إلى callback
end)

------------------------------------
---كلبشة/إقتياد/إدخال مركبة START---
------------------------------------
 
--[[RegisterServerEvent('esx_misc:startAreszt')
AddEventHandler('esx_misc:startAreszt', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)
    local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' or xPlayer.job.name == 'admin' then
	TriggerClientEvent('esx_misc:aresztowany', targetPlayer.source, source)
	TriggerClientEvent('esx_misc:aresztuj', source)
	else
	print(('esx_misc: %s attempted to cuff (not cop)!'):format(xPlayer.identifier))
	end
end)]]

RegisterServerEvent('esx_misc:unhandcuff')
AddEventHandler('esx_misc:unhandcuff', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)
    local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' or xPlayer.job.name == 'admin' then
		
		TriggerClientEvent('esx_misc:unhand', targetPlayer.source, source)
	    TriggerClientEvent('esx_misc:unhands', source)
	else
	print(('esx_misc: %s attempted to unhandcuff (not cop)!'):format(xPlayer.identifier))
	end
end)

ESX.RegisterServerCallback('esx_misc:checkAdminPermission', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer and xPlayer.getGroup then
        local group = xPlayer.getGroup()
        if group == 'admin' or group == 'superadmin' then
            cb(true)
        else
            cb(false)
        end
    else
        cb(false)
    end
end)

RegisterServerEvent('esx_misc:drag')
AddEventHandler('esx_misc:drag', function(target)
	local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' or xPlayer.job.name == 'admin' then
		TriggerClientEvent('esx_misc:drag', target, source)
	else
		print(('esx_misc: %s attempted to drag (not cop)!'):format(xPlayer.identifier))
	end
end)

RegisterServerEvent('esx_misc:putInVehicle')
AddEventHandler('esx_misc:putInVehicle', function(target)
	local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' or xPlayer.job.name == 'admin' then
		TriggerClientEvent('esx_misc:putInVehicle', target)
	else
		print(('esx_misc: %s attempted to put in vehicle (not cop)!'):format(xPlayer.identifier))
	end
end)

RegisterServerEvent('esx_misc:OutVehicle')
AddEventHandler('esx_misc:OutVehicle', function(target)
	local xPlayer = ESX.GetPlayerFromId(source)

	if xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' or xPlayer.job.name == 'admin' then
		TriggerClientEvent('esx_misc:OutVehicle', target)
	else
		print(('esx_misc: %s attempted to drag out from vehicle (not cop)!'):format(xPlayer.identifier))
	end
end)
------------------------------------
---كلبشة/إقتياد/إدخال مركبة END---
------------------------------------
local Cooldown_count = 0
	-- function Cooldown(sec)
		--CreateThread(function()
		--	Cooldown_count = sec
		--	while Cooldown_count ~= 0 do
			--	Cooldown_count = Cooldown_count - 1
			---	Wait(1000)
			--end	
		--	Cooldown_count = 0
		--end)	
	--end
----------------
---PanicButton--
----------------

Pnaic_Data = { -- chatStart='عند البدأ', chatEnd='عند إطفاء حالة الأمن تظهر هاذه الرسالة',
    --# admin things
	['peace_time'] = {state=false, timePeriod=0, chatEnd='تم إنتهاء ^6وقت الراحة^0', Thelabel='وقت راحة', DrawText='ﺔﺣﺍﺭ ﺖﻗﻭ', Thecoords=vector3(124.6,1676.8,228.3), Thedistance=7000.0, isRedZone=false, isBlackZone=false},
	['restart_time'] = {state=false, timePeriod=0, chatEnd='تم إنتهاء ^1وقت ريستارت^0', Thelabel='ﻭﻗﺖ ﺭﻳﺴﺘﺎﺭﺕ', DrawText='ﺕﺭﺎﺘﺴﻳﺭ ﺖﻗﻭ', Thecoords=vector3(124.6,1676.8,228.3), Thedistance=7000.0, isRedZone=false, isBlackZone=false},
	['hacker'] = {chatStart='تم إطلاق صافرة إنذار ^3مكافحة الخكر^0 الرجاء التحلي بالهدوء الى أن يتم التخلص من الحثالة', chatEnd='إنتهاء صافرة إنذار ^3مكافحة الخكر^0. شكرا لتعاونكم',state=false, Thelabel='مكافحة الخكر', DrawText='ﺮﻜﺨﻟﺍ ﺔﺤﻓﺎﻜﻣ', Thecoords=vector3(124.6,1676.8,228.3), Thedistance=7000.0, isRedZone=false, isBlackZone=false},
	
	['my_location_safezone'] = {chatStart='تم إعلان حالة ^2ﻣﻮﻗﻊ ﺁﻣﻦ^0 الرجاء اتباع ارشادات المراقب في ^2الموقع^0 والالتزام بالقوانين', chatEnd='إنتهاء حالة ^2ﻣﻮﻗﻊ ﺁﻣﻦ^0', state=false, Thelabel='ﻣﻮﻗﻊ ﺁﻣﻦ', DrawText='ﻦﻣﺁ ﻊﻗﻮﻣ', ThecoordsN=nil, Thedistance=250.0, isRedZone=false, isBlackZone=false},
	['restricted_area'] = {chatStart='تم إعلان ^3منطقة محظورة^0 يجب الأبتعاد وعدم المرور ^3بالمنطقة^0 وإتباع إرشادات المراقب', chatEnd='إنتهاء ^3منطقة محظورة^0 يمكن الذهاب و المرور بالمنطقة الأن', state=false, Thelabel='ﻣﻨﻄﻘﺔ ﻣﺤﻈﻮﺭﺓ', DrawText='ﺓﺭﻮﻈﺤﻣ ﺔﻘﻄﻨﻣ', ThecoordsN=nil, Thedistance=250.0, isRedZone=false, isBlackZone=true},
	
	['event_start'] = {chatStart='تم إعلان ^6نقطة بداية فعالية', chatEnd='إنتهاء ^6نقطة بداية فعالية', state=false, Thelabel='', DrawText='ﺔﻴﻟﺎﻌﻓ ﺔﻳﺍﺪﺑ ﺔﻄﻘﻧ', ThecoordsN=nil, Thedistance=250.0, isRedZone=false, isBlackZone=false},--نقطة بداية فعالية
	['event_location'] = {chatStart='تم إعلان ^6موقع الفعالية', chatEnd='إنتهاء ^6موقع الفعالية', state=false, Thelabel='', DrawText='ﺔﻴﻟﺎﻌﻔﻟﺍ ﻊﻗﻮﻣ', ThecoordsN=nil, Thedistance=250.0, isRedZone=false, isBlackZone=false},--موقع الفعالية
	['event_registration'] = {chatStart='تم إعلان ^6موقع تسجيل الفعالية', chatEnd='إنتهاء ^6موقع تسجيل الفعالية', state=false, Thelabel='', DrawText='ﺔﻴﻟﺎﻌﻔﻟﺍ ﻞﻴﺠﺴﺗ ﻊﻗﻮﻣ', ThecoordsN=nil, Thedistance=250.0, isRedZone=false, isBlackZone=false},--موقع تسجيل الفعالية
	['event_end'] = {chatStart='تم إعلان ^6نقطة نهاية الفعالية', chatEnd='إنتهاء ^6نقطة نهاية الفعالية', state=false, Thelabel='', DrawText='ﺔﻴﻟﺎﻌﻔﻟﺍ ﺔﻳﺎﻬﻧ ﺔﻄﻘﻧ', ThecoordsN=nil, Thedistance=250.0, isRedZone=false, isBlackZone=false},--نقطة نهاية الفعالية
	--# Stnfar/helpme
	['helpme'] = {chatStart='^8نداء إستغاثة', chatEnd='إنتهاء ^3نداء إستغاثة', state=false, Thelabel='نداء إستغاثة', DrawText='ﺔﺛﺎﻐﺘﺳﺇ ﺀﺍﺪﻧ', ThecoordsN=nil, Thedistance=100.0, isRedZone=true, isBlackZone=false},
	
	['my_location'] = {chatStart='تجربة بدأ استنفار مكان عام', chatEnd='تجربة إنهاء استنفار مكان عام', state=false, Thelabel='مكان عام', DrawText='ﻡﺎﻋ ﻥﺎﻜﻣ', ThecoordsN=nil, Thedistance=250.0, isRedZone=true, isBlackZone=false},
	--# Ports/airports Stnfar
	['sea_port'] = {state=false, Thelabel='الميناء البحري الرئيسي', DrawText='ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', Thecoords=vector3(1025.65,-3116.86,5.9), Thedistance=350.0, isRedZone=true, isBlackZone=false},
	['seaport_west'] = {state=false, Thelabel='الميناء البحري الغربي', DrawText='ﻲﺑﺮﻐﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', Thecoords=vector3(-82.3909,-2446.72,6.0085), Thedistance=190.0, isRedZone=true, isBlackZone=false},
	['international_airport'] = {state=false, Thelabel='المطار الدولي', DrawText='ﻲﻟﻭﺪﻟﺍ ﺭﺎﻄﻤﻟﺍ', Thecoords=vector3(-1329.27,-2487.14,13.945), Thedistance=450.0, isRedZone=true, isBlackZone=false},
	['sandy_airport'] = {state=false, Thelabel='مطار ساندي', DrawText='ﻱﺪﻧﺎﺳ ﺭﺎﻄﻣ', Thecoords=vector3(1572.59,3194.25,40.85), Thedistance=300.0, isRedZone=true, isBlackZone=false},
	['farm_airport'] = {state=false, Thelabel='مطار المزارع', DrawText='ﻉﺭﺍﺰﻤﻟﺍ ﺭﺎﻄﻣ', Thecoords=vector3(2058.22,4772.77,41.11), Thedistance=150.0, isRedZone=true, isBlackZone=false},
	--# Banks
	['pacific_bank'] = {state=false, Thelabel='البنك المركزي', DrawText='ﻱﺰﻛﺮﻤﻟﺍ ﻚﻨﺒﻟﺍ', Thecoords=vector3(236.81,217.99,106.29), Thedistance=250.0, isRedZone=true, isBlackZone=false},
	['paleto_bank'] = {state=false, Thelabel='بنك بليتو', DrawText='ﻮﺘﻴﻠﺑ ﻚﻨﺑ', Thecoords=vector3(-109.27,6464.39,31.63), Thedistance=200.0, isRedZone=true, isBlackZone=false},
	['sandy_bank'] = {state=false, Thelabel='بنك ساندي', DrawText='ﻱﺪﻧﺎﺳ ﻚﻨﺑ', Thecoords=vector3(1174.95,2705.32,38.09), Thedistance=250.0, isRedZone=true, isBlackZone=false},
	--# public_garage
	['public_car_garage_los_santos'] = {state=false, Thelabel='كراج سيارات لوس', DrawText='ﺱﻮﻟ ﺕﺍﺭﺎﻴﺳ ﺝﺍﺮﻛ', Thecoords=vector3(207.32,-1464.71,29.15), Thedistance=250.0, isRedZone=true, isBlackZone=false},
	['public_car_garage_sandy'] = {state=false, Thelabel='كراج سيارات ساندي', DrawText='ﻱﺪﻧﺎﺳ ﺕﺍﺭﺎﻴﺳ ﺝﺍﺮﻛ', Thecoords=vector3(1729.4,3711.37,34.22), Thedistance=250.0, isRedZone=true, isBlackZone=false},
	['public_car_garage_paleto'] = {state=false, Thelabel='كراج سيارات بوليتو', DrawText='ﻮﺘﻴﻟﻮﺑ ﺕﺍﺭﺎﻴﺳ ﺝﺍﺮﻛ', Thecoords=vector3(-216.32,6251.73,31.48), Thedistance=250.0, isRedZone=true, isBlackZone=false},
	['public_car_garage_paleto1'] = {state=false, Thelabel='كراج سيارات بوليتو', DrawText='ﻮﺘﻴﻟﻮﺑ ﺕﺍﺭﺎﻴﺳ ﺝﺍﺮﻛ', Thecoords=vector3(847.1232, -2180.00, 30.299), Thedistance=250.0, isRedZone=true, isBlackZone=false},

	--# Other location
	['army_base'] = {state=false, Thelabel='القاعدة العسكرية', DrawText='ﺔﻳﺮﻜﺴﻌﻟﺍ ﺓﺪﻋﺎﻘﻟﺍ', Thecoords=vector3(-2169.85,3121.25,32.82), Thedistance=500.0, isRedZone=true, isBlackZone=false},
	['alshaheed_gardeen'] = {state=false, Thelabel='الحديقة العامة', DrawText='ﺔﻣﺎﻌﻟﺍ ﺔﻘﻳﺪﺤﻟﺍ', Thecoords=vector3(214.63,-1028.35,29.34), Thedistance=250.0, isRedZone=true, isBlackZone=false},
	
	--# ports close
	['sea_port_close'] = {chatStart='تم إعلان اغلاق ^3الميناء البحري الرئيسي^0 يمنع دخول ^3الموقع^0 حتى يتم اعلان الافتتاح', chatEnd='تم افتتاح ^3الميناء البحري الرئيسي^0 يمكنك الاَن بيع واستلام البضائع من الموقع', state=false, Thelabel='ﺍﻟﻤﻴﻨﺎﺀ ﺍﻟﺒﺤﺮﻱ ﺍﻟﺮﺋﻴﺴﻲ', DrawText='ﻖﻠﻐﻣ ﻲﺴﻴﺋﺮﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', Thecoords=vector3(1025.65,-3116.86,5.9), Thedistance=320.0, isRedZone=false, isBlackZone=true},
	['seaport_west_close'] = {chatStart='تم إعلان اغلاق ^3الميناء البحري الغربي^0 يمنع دخول ^3الموقع^0 حتى يتم اعلان الافتتاح', chatEnd='تم افتتاح ^3الميناء البحري الغربي^0 يمكنك الاَن بيع واستلام البضائع من الموقع', state=false, Thelabel='ﺍﻟﻤﻴﻨﺎﺀ ﺍﻟﺒﺤﺮﻱ ﺍﻟﻐﺮﺑﻲ', DrawText='ﻖﻠﻐﻣ ﻲﺑﺮﻐﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', Thecoords=vector3(-82.3909,-2446.72,6.0085), Thedistance=200.0, isRedZone=false, isBlackZone=true},
	['internationa_close'] = {chatStart='تم إعلان اغلاق ^3المطار الدولي^0 يمنع دخول ^3الموقع^0 حتى يتم اعلان الافتتاح', chatEnd='تم افتتاح ^المطار الدولي^0 يمكنك الاَن بيع واستلام البضائع من الموقع', state=false, Thelabel='ﺍﻟﻤﻄﺎﺭ ﺍﻟﺪﻭﻟﻲ', DrawText='ﻖﻠﻐﻣ ﻲﻟﻭﺪﻟﺍ ﺭﺎﻄﻤﻟﺍ', Thecoords=vector3(-1329.27,-2487.14,13.945), Thedistance=500.0, isRedZone=false, isBlackZone=true},
	
}

--لاتعدل نهائيا START
Time_Data = {
	[1] = 60000,
	[2] = 120000,
	[3] = 180000,
	[4] = 240000,
	[5] = 300000,
	[6] = 360000,
	[7] = 420000,
	[8] = 480000,
	[9] = 54000,
	[10] = 600000,
	[11] = 1,
}
--لاتعدل نهائيا END


local alarm, WaitForNewStart = {}, false
RegisterServerEvent('esx_misc:TogglePanicButton')
AddEventHandler('esx_misc:TogglePanicButton', function(data1, data2, data3, data4)
	local src = source
	local xPlayer = ESX.GetPlayerFromId(source)
	
	local rpname = xPlayer.getName()
	local locationsData = Config.panicButton.locationsData
	
	local deleteAllAlarms = false
	local PanicName = data1
	local coordss = data2
	local distance = 250.0
	local DrawText = ''
	local labell = ''
	local StartAfrer = ''
	if xPlayer.job.name == 'admin' or xPlayer.job.name == 'police' or xPlayer.job.name == 'agent' then
	
	
	-- if Cooldown_count > 0 then 
	-- xPlayer.showNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..Cooldown_count..' ثانية')
	-- return
	-- end
	-- Cooldown(5)
	  --# peace/restart time
	  if PanicName == 'peace_time' or PanicName == 'restart_time' and not WaitForNewStart then -- 60000
	  Pnaic_Data[PanicName].timePeriod = data4
	  elseif ( PanicName == 'peace_time' or PanicName == 'restart_time' ) and WaitForNewStart then
	  TriggerClientEvent('esx:showNotification', source, 'يوجد وقت راحة/ريستارت سوف يفعل بالفعل أنتظر')
	  return
	  end
	  --# coords
	  if Pnaic_Data[PanicName].Thecoords then
	  coordss = Pnaic_Data[PanicName].Thecoords
	  else
	  Pnaic_Data[PanicName].ThecoordsN = coordss
	  end
	  --# distance
	  if Pnaic_Data[PanicName].Thedistance then
	  distance = Pnaic_Data[PanicName].Thedistance
	  end
	  --# name/DrawText
      if Pnaic_Data[PanicName].DrawText then
	  TheDrawText = Pnaic_Data[PanicName].DrawText
	  end
	  --# label
      if Pnaic_Data[PanicName].Thelabel then
	  labell = Pnaic_Data[PanicName].Thelabel
	  end
	  --# chat for Stnfar
      if not locationsData[PanicName] then
	  
	  Pnaic_Data[PanicName].chatStart = 'استنفار امني في ^3'..Pnaic_Data[PanicName].Thelabel..'^0 الرجاء مغادرة ^3الموقع^0 فورا وعدم الاقتراب او محاولة الدخول حتى انتهاء حالة الخطر'
	  Pnaic_Data[PanicName].chatEnd = 'إنتهاء حالة الخطر في ^3'..Pnaic_Data[PanicName].Thelabel..'^0'
	  
	  end
	  
	  print('^3'..GetPlayerName(source)..'^0 Has Toggled ^2'..data1..'^0 Panic Button ^5'..xPlayer.identifier..'^0')
	  
	  --# التحقق اذا كان مفعل من قبل ويتم الإلغاء
	  WASremoved = false
	  if Pnaic_Data[PanicName].state or Pnaic_Data[PanicName].isRedZone or Pnaic_Data[PanicName].isBlackZone then
	  toRemove = 0

			if #alarm > 0 then
				for i=1, #alarm, 1 do
					if alarm[i].location == PanicName then
					    if Pnaic_Data[PanicName].isRedZone or Pnaic_Data[PanicName].isBlackZone and data3 then
							if alarm[i].coords == coordss then
								alarm[i].siren = false
								toRemove = i
							end
						else
							alarm[i].siren = false
							toRemove = i
						end
					end
				end
			end

			if toRemove > 0 then
                Pnaic_Data[PanicName].state = false
                TriggerClientEvent('esx_misc:updateStatus', -1, alarm, PanicName)
                WASremoved = true
                Wait(50)
                TriggerClientEvent("zahya_police_safe_zone:CL:RemoveZones", src, coordss,PanicName)
                TriggerClientEvent("zahya_police_safe_black_zone:CL:RemoveZones", src, coordss)
                if alarm ~= 'helpme' or alarm ~= 'my_location' then
                    table.remove(alarm, toRemove)
                end
            end
		end
		
		if WASremoved then
		--CHAT START
		MSGS = Pnaic_Data[PanicName].chatEnd
	  if xPlayer["job"]["name"] == "admin" then
	  TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  MSGS)
	  elseif xPlayer["job"]["name"] == "police" then
	   TriggerClientEvent('chatMessage', -1, "👮 إدارة الشرطة | " .. rpname .." " ,  { 17, 69, 191 }, MSGS)
	  elseif xPlayer["job"]["name"] == "agent" then
	   TriggerClientEvent('chatMessage', -1, "💂‍ حرس الحدود  | " .. rpname .." " ,  { 78, 198, 78 }, MSGS)	
	    elseif xPlayer["job"]["name"] == "ambulance" then
	   TriggerClientEvent('chatMessage', -1, " 🚨 الهلال الاحمر | " .. rpname .." " ,  { 196, 20, 20 }, MSGS)		
	   else
	   return
	  end
	   --CHAT END
		return
		end
		
	  --[[
	  if PanicName == 'peace_time' then
	  PeaceTimetiming(Pnaic_Data['peace_time'].PeacetimePeriod)
	  elseif PanicName == 'restart_time' then
	  RestartTimetiming(Pnaic_Data['restart_time'].PeacetimePeriod)
	  end]]
	  
	 --TriggerClientEvent("esx_misc:updatePeacetimePeriod", -1, Thetime)
	 --PeaceTimetiming(Thetime)
	 
	  --timing(Config.time)
	   if PanicName == 'peace_time' or PanicName == 'restart_time' then -- 60000
	   --TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  "Peace/Restart Time will start after "..data3.." min")
	   --TriggerEvent('_chat:messageEntered', GetPlayerName(source), { 0, 0, 0 }, Pnaic_Data[PanicName].chatStart)
	   if data3 ~= 11 then
	   if PanicName == 'peace_time' then
	   TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  ' تم إعلان ^6وقت راحة^0 لمدة ^6'..Pnaic_Data[PanicName].timePeriod..'^0 دقيقة . يبدأ بعد ^3'..data3..'^0 دقيقة')
	   elseif PanicName == 'restart_time' then
	   TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  ' تم إعلان ^1وقت ريستارت^0 لمدة ^1'..Pnaic_Data[PanicName].timePeriod..'^0 دقيقة . يبدأ بعد ^3'..data3..'^0 دقيقة')
	   end
	   end
	   WaitForNewStart = true
	   Wait(Time_Data[data3])
	   WaitForNewStart = false
		
		if #alarm > 0 then
				for i=1, #alarm, 1 do
					    alarm[i].siren = false
				end
			end
			Wait(1)
		TriggerClientEvent('esx_misc:updateStatus_notifieNO', -1, alarm)
		Wait(1)
		
	  TriggerEvent('esx_misc:timing_panic', Pnaic_Data[PanicName].timePeriod, PanicName)
	  end
	  
	  
	  --# في حال الوصول لهاذه النقطة ذالك يعني ان حالة الأمن هاذه غير فعالة حاليا وسوف يتم تفعيلها الأن
	  --CHAT START
	  if PanicName == 'peace_time' then
	   TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  'تم إعلان ^6وقت راحة^0 لمدة '..'^6'..Pnaic_Data[PanicName].timePeriod..'^0 دقيقة'..' يمنع العمل الاجرامي وتعطل جميع الدوائر الحكومية والخاصة حتى انقضاء المدة')
	   elseif PanicName == 'restart_time' then
	   TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  'تم إعلان ^1وقت ريستارت^0 لمدة '..'^1'..Pnaic_Data[PanicName].timePeriod..'^0 دقيقة'..' يمنع عمل سناريو جديد وحاول ان تفصل قبل انقضاء الوقت المحدد.تشدد عقوبة التخريب اثناء وقت الرستارت.')
	   else
	   MSGS = Pnaic_Data[PanicName].chatStart
	  if xPlayer["job"]["name"] == "admin" then
	  TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  MSGS)
	  elseif xPlayer["job"]["name"] == "police" then
	   TriggerClientEvent('chatMessage', -1, "👮 إدارة الشرطة | " .. rpname .." " ,  { 17, 69, 191 }, MSGS)
	  elseif xPlayer["job"]["name"] == "agent" then
	   TriggerClientEvent('chatMessage', -1, "💂‍ حرس الحدود  | " .. rpname .." " ,  { 78, 198, 78 }, MSGS)	
	    elseif xPlayer["job"]["name"] == "ambulance" then
	   TriggerClientEvent('chatMessage', -1, " 🚨 الهلال الاحمر | " .. rpname .." " ,  { 196, 20, 20 }, MSGS)		
	  end
	  end
	  --CHAT END

	  
	  table.insert(alarm, {location = PanicName, name = TheDrawText, coords=coordss, dist = distance, siren = true, label = labell})
	  if not Pnaic_Data[PanicName].isRedZone or not Pnaic_Data[PanicName].isBlackZone then
		Pnaic_Data[PanicName].state = true
	  end
	  -- if Pnaic_Data[PanicName].isRedZone then
	    -- TriggerEvent("zahya_police_safe_zone:SV:CreateZone", coordss, distance)
	  -- elseif Pnaic_Data[PanicName].isBlackZone then
	    -- TriggerEvent("zahya_police_safe_black_zone:SV:CreateZone", coordss, distance)
	  -- end
	  Wait(100)
	  TriggerClientEvent('esx_misc:updateStatus', -1, alarm, PanicName)
	  
	  else
	  print(('esx_misc: ^1%s^0 attempted to TogglePanicButton %s (not allowed!)!'):format(xPlayer.identifier, data1))
	  end
end)



RegisterServerEvent('esx_misc:cheakStatus')
AddEventHandler('esx_misc:cheakStatus', function()

	TriggerClientEvent('esx_misc:updateStatus', source, alarm, nil, true)

end)

--Other
--[[ -- not in use and it don't work will
function deleteAllAlarms(PanicName)
	if PanicName == 'peace_time' or PanicName == 'restart_time' then 
			deleteAllAlarms = true
		else
			deleteAllAlarms = false
		end
	end
	
	if deleteAllAlarms then
	
	if #alarm > 0 then
				for i=1, #alarm, 1 do
					if alarm[i].siren then
					    alarm[i].siren = false
					end
					table.remove(alarm, i)
				end
			end
	
end]]

function isNoCrimetime()
	local noCrimeZone = Config.panicButton.noCrimeZone
	local data = {}
	for k,v in pairs (alarm) do
		if v.siren then
			for i=1,#noCrimeZone,1 do
				if v.location == noCrimeZone[i] then
					data.active = true
					data.location = v.location
					data.label = v.label
					return data
				end	
			end
		end
	end
	--print('# no crime zone found')
	data.active = false
	return data
end
--

function timing_panic(time, PanicName)
    if time == 0 then
	if  Pnaic_Data[PanicName].state then
	toRemove = 0
	if #alarm > 0 then
				for i=1, #alarm, 1 do
					if alarm[i].location == PanicName then
					    alarm[i].siren = false
						toRemove = i
					end
				end
			end

			if toRemove > 0 then
			    Pnaic_Data[PanicName].state = false
				TriggerClientEvent('esx_misc:updateStatus', -1, alarm, PanicName)
				TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  Pnaic_Data[PanicName].chatEnd)
				--Wait(50)
				--table.remove(alarm, toRemove)
				
			end
			else
			return
			end
	end
    TriggerClientEvent("esx_misc:updatePeacetimePeriod", -1, time)
    Wait(60000)
    if Pnaic_Data[PanicName].timePeriod > 0 then 
        Pnaic_Data[PanicName].timePeriod = time - 1
        timing_panic(Pnaic_Data[PanicName].timePeriod, PanicName) 
    end
end

RegisterNetEvent('esx_misc:timing_panic')
AddEventHandler('esx_misc:timing_panic', function(time, PanicName)
    timing_panic(time, PanicName)
end)

--------------------
---PanicButton END--
--------------------
--------------------
---Players blips ---
--------------------
-- local PlayersBlips = {}

-- Citizen.CreateThread(function()
--    Wait(100)

--    while Config.EnablePlayersBlips do
--     Wait(1000 * 60 *  3) -- 3 mins
--       --Wait(1000 * 5) -- 5 sec for tests

-- 	   TriggerClientEvent('esx_misc:updateBlips', -1, PlayersBlips)

--    end

-- end)

-- Citizen.CreateThread(function()

-- 	Wait(100)

-- 	while true do

-- 		Wait(1000 * 30) -- 30 sec
		
	
-- 		   TriggerClientEvent('esx_misc:onlineplayers', -1, playersOnline)
	
-- 	   end

-- end)


----------------------
---Ports/Mina START---
----------------------
local lasttime = 0
RegisterServerEvent("esx_misc:togglePort")
AddEventHandler("esx_misc:togglePort", function(newport)
    local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.job.name == 'admin' or (xPlayer.job.name == 'agent' and xPlayer.job.grade >= 7) or (xPlayer.job.name == 'police' and xPlayer.job.grade >= 7) then
	if newport ~= currentPort then
	if os.time() - lasttime >= 12 then 
	lasttime = os.time()
	--Cooldown(12)
	if newport == 1 then
	MSG = 'الميناء البحري الرئيسي'
	currentPort = 1
	TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
	TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
	MinaPlaceLog((' تحويل موقع التصدير '),"موقع التصدير الميناء البحري الرئيسي", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********)
	elseif newport == 2 then
	MSG = 'الميناء البحري الغربي'
	currentPort = 2
	TriggerClientEvent("mina:changePort_cl", -1, 2)
	TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
	TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
	MinaPlaceLog((' تحويل موقع التصدير '),"موقع التصدير الميناء البحري الغربي", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********)
   elseif newport == 3 then
   MSG = 'المطار الدولي'
	currentPort = 3
	TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
	TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
	MinaPlaceLog((' تحويل موقع التصدير '),"موقع التصدير المطار الدولي", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********)
	elseif newport == 11 then
		MSG = 'الميناء البحري توسعة 1'
		currentPort = 11
		TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
		TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
		MinaPlaceLog((' افتتاح توسعة 1 '),"الميناء البحري توسعة 1", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	elseif newport == 12 then
		MSG = 'الميناء البحري توسعة 2'
		currentPort = 12
		TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
		TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
		MinaPlaceLog((' افتتاح توسعة 2 '),"الميناء البحري توسعة 2", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	elseif newport == 13 then
		MSG = 'الميناء البحري توسعة 3'
		currentPort = 13
		TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
		TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
		MinaPlaceLog((' افتتاح توسعة 3 '),"الميناء البحري توسعة 3", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	elseif newport == 14 then
		MSG = 'الميناء البحري توسعة 4'
		currentPort = 14
		TriggerClientEvent("esx_misc:currentPortNum", -1, currentPort)
		TriggerClientEvent('esx_misc:watermark_port', -1, currentPort)
		MinaPlaceLog((' افتتاح توسعة 4 '),"الميناء البحري توسعة 4", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	elseif newport == "location_sell_drugs_1" then
		MSG = 'موقع 1'
		currentPort = "location_sell_drugs_1"
		TriggerClientEvent("esx_misc:currentPortNumDrugs", -1, currentPort)
		MinaPlaceLog((' افتتاح موقع تهريب 1 '),"افتتاح موقع تهريب 1", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	elseif newport == "location_sell_drugs_2" then
		MSG = 'موقع 2'
		currentPort = "location_sell_drugs_2"
		TriggerClientEvent("esx_misc:currentPortNumDrugs", -1, currentPort)
		MinaPlaceLog((' افتتاح موقع تهريب 2 '),"افتتاح موقع تهريب 2", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	elseif newport == "location_sell_drugs_3" then
		MSG = 'موقع 3'
		currentPort = "location_sell_drugs_3"
		TriggerClientEvent("esx_misc:currentPortNumDrugs", -1, currentPort)
		MinaPlaceLog((' افتتاح موقع تهريب 3 '),"افتتاح موقع تهريب 3", "*من قبل* \n"..xPlayer.job.label.."\nSteam: `".. GetPlayerName(source).."` \n "..GetPlayerIdentifiers(source)[1].." \n "..GetPlayerIdentifiers(source)[5].." \nInGame name: `"..xPlayer.getName().."`",********) 
	end
   --CHAT START
	  if xPlayer["job"]["name"] == "admin" then
	  MSGS = 'منطقة التصدير الاَن ^3'..MSG
	  TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة و التفتيش  " ,  {198, 40, 40} ,  MSGS)
	  elseif xPlayer["job"]["name"] == "police" then
	   TriggerClientEvent('chatMessage', -1, "👮 إدارة الشرطة | " .. rpname .." " ,  { 17, 69, 191 }, MSGS)
	  elseif xPlayer["job"]["name"] == "agent" then
	   TriggerClientEvent('chatMessage', -1, "💂‍ حرس الحدود  | " .. rpname .." " ,  { 78, 198, 78 }, MSGS)	
	    elseif xPlayer["job"]["name"] == "ambulance" then
	   TriggerClientEvent('chatMessage', -1, " 🚨 الهلال الاحمر | " .. rpname .." " ,  { 196, 20, 20 }, MSGS)		
	   else
	   return
	  end
	  --CHAT END
   else
	xPlayer.showNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'.. 12 - (os.time() - lasttime)..' ثانية')
	end
   else
   TriggerClientEvent('esx:showNotification', source, 'موقع التصدير هاذه فعال بالفعل')
   end
   else
   print(('esx_misc: %s attempted to change port (not allowed!)!'):format(xPlayer.identifier))
   end
end)


----------------------
---Ports/Mina END-----
----------------------

-----------------------------------------
---ممنوع الأجرام وحاجات مثل كذا START-----
-----------------------------------------
local NoCrimetime = {}

NoCrimetime_Data = {
	['NoCrimetime'] = {label='ممنوع الأجرام', state=false,timer=false,min=0,sec=00, Cancel=false},
	['NoCrimetimeSandy'] = {label='ممنوع الأجرام ساندي ', state=false,timer=false,min=0,sec=00, Cancel=false},
	['NoCrimetimePoleto'] = {label='ممنوع الأجرام وبوليتو', state=false,timer=false,min=0,sec=00, Cancel=false},
	['NoCrimetimeCayo'] = {label='ممنوع الأجرام جزيرة نجم', state=false,timer=false,min=0,sec=00, Cancel=false},
	['NoCrimetimeLos'] = {label='ممنوع الأجرام لوس سانتوس', state=false,timer=false,min=0,sec=00, Cancel=false},
	['NewScenario'] = {label='ممنوع بدأ سيناريو جديد', state=false,timer=false,min=0,sec=00, Cancel=false},
	['StartRobbery'] = {label='وقت السرقة', state=false,timer=false,min=0,sec=00, Cancel=false},
	
	['MainBank'] = {label='ممنوع سرقة البنك المركزي', state=false,timer=false,min=0,sec=00, Cancel=false},
	['SmallBanks'] = {label='ممنوع سرقة البنوك الصغيرة', state=false,timer=false,min=0,sec=00, Cancel=false},
	['Stores'] = {label='ممنوع سرقة المتاجر', state=false,timer=false,min=0,sec=00, Cancel=false},
	['SellDrugs'] = {label='ممنوع تهريب الممنوعات', state=false,timer=false,min=0,sec=00, Cancel=false},
}

function isNoCrimetime2() --return table with 3 values {active(boolen),name,label}
    local noCrimeZone2 = Config.panicButton.noCrimeTime2
	local data2 = {}
	for k,v in pairs (NoCrimetime) do
		if v.active then
			for i=1,#noCrimeZone2,1 do
				if v.name == noCrimeZone2[i] then
					data2.active = true
					data2.name = v.name
					data2.label = v.label
					return data2
				end	
			end
		end
	end
	--print('# no crime zone found')
	data2.active = false
	return data2
end


RegisterServerEvent("esx_misc:NoCrimetime")
AddEventHandler('esx_misc:NoCrimetime', function(promotionName, isToggle, min)
    local xPlayer = ESX.GetPlayerFromId(source)
    
	if xPlayer.job.name == 'admin' then
	if isToggle then
	print(('esx_misc: ^3%s^0 Has Toggled '..promotionName):format(GetPlayerName(source)))
	if NoCrimetime_Data[promotionName].state then
	TriggerClientEvent('esx_misc:watermark_promotion', -1, promotionName, false, 2)
	NoCrimetime_Data[promotionName].state = false
	
	if #NoCrimetime > 0 then
				for i=1, #NoCrimetime, 1 do
					if NoCrimetime[i].name == promotionName then
						NoCrimetime[i].active = false
					end
				end
			end
			TriggerClientEvent('esx_misc:updateNoCrimetime', -1, NoCrimetime)
	else
	TriggerClientEvent('esx_misc:watermark_promotion', -1, promotionName, true, 2)
	NoCrimetime_Data[promotionName].state = true
	table.insert(NoCrimetime, {name = promotionName, label = NoCrimetime_Data[promotionName].label, active=true})
	TriggerClientEvent('esx_misc:updateNoCrimetime', -1, NoCrimetime)
	end
	
	elseif not isToggle then
	NoCrimetime_Data[promotionName].min = min
	NoCrimetime_Data[promotionName].sec = 0
	--NoCrimetime_Data[promotionName].Cancel = true
	
	print(('esx_misc: ^3%s^0 Has rest the time to ^1%s^0 min for %s'):format(GetPlayerName(source),min,promotionName))
	if min > 0 and not NoCrimetime_Data[promotionName].timer then
	timing(promotionName)
	end
	end
	else
	print(('esx_misc: %s attempted to use esx_misc:NoCrimetime (not adminjob!)!'):format(xPlayer.identifier))
	end
end)

function timing(promotionName, pasCancel)
    NoCrimetime_Data[promotionName].timer = true
	
    min = NoCrimetime_Data[promotionName].min
    sec = NoCrimetime_Data[promotionName].sec
	
	if min == 111 then
	NoCrimetime_Data[promotionName].timer = false
	TriggerClientEvent("esx_misc:updatePromotionTimer", -1, promotionName, false)
	return
	end
	
	--if NoCrimetime_Data[promotionName].Cancel and not pasCancel then NoCrimetime_Data[promotionName].Cancel = false TriggerClientEvent("esx_misc:updatePromotionTimer", -1, promotionName, false) return end
	if sec == 0 and NoCrimetime_Data[promotionName].min ~= 0 then
	NoCrimetime_Data[promotionName].min = min - 1
	NoCrimetime_Data[promotionName].sec = 59
	end
    if min == 0 and NoCrimetime_Data[promotionName].sec == 0 then
	NoCrimetime_Data[promotionName].timer = false
	if NoCrimetime_Data[promotionName].state then
	NoCrimetime_Data[promotionName].state = false
	TriggerClientEvent('esx_misc:watermark_promotion', -1, promotionName, false, 2)
	
	if #NoCrimetime > 0 then
				for i=1, #NoCrimetime, 1 do
					if NoCrimetime[i].name == promotionName then
						NoCrimetime[i].active = false
					end
				end
			end
			TriggerClientEvent('esx_misc:updateNoCrimetime', -1, NoCrimetime)
	return
	else
	return
	end
	end
    TriggerClientEvent("esx_misc:updatePromotionTimer", -1, promotionName, true, NoCrimetime_Data[promotionName].min, NoCrimetime_Data[promotionName].sec)
    Wait(1000)
        
		if NoCrimetime_Data[promotionName].sec ~= 0 then
        NoCrimetime_Data[promotionName].sec = NoCrimetime_Data[promotionName].sec - 1
		end
        timing(promotionName) 
end

----------------------
---Teleport START-----
----------------------

RegisterServerEvent("esx_misc:GiveTeleportMenu")
AddEventHandler('esx_misc:GiveTeleportMenu', function(To)
    local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.job.name == 'admin' then
    if To == "all" then
    TriggerClientEvent("esx_misc:StartTeleport", -1, "all")
	print('^3'..GetPlayerName(source)..'^0 Has gived TeleportMenu To all')
	else
	local xTarget = ESX.GetPlayerFromId(To)
	if xTarget then
	TriggerClientEvent("esx_misc:StartTeleport", To)
	TriggerClientEvent('esx:showNotification', source, 'تم إعطاء <font color=yellow>'..xTarget.getName()..'</font> قائمة الأنتقال')
	TriggerClientEvent('esx:showNotification', To, 'تم إعطائك قائمة الانتقال من قبل <font color=red>المراقب</font>')
	print('^3'..GetPlayerName(source)..'^0 Has gived TeleportMenu To ^5'..GetPlayerName(To)..'^0')
	else
	TriggerClientEvent('esx:showNotification', source, 'اللاعب غير متصل')
	end
	end
	else
	print(('esx_misc: %s attempted to GiveTeleportMenu (not allowed!)!'):format(xPlayer.identifier))
	end
end)


--[[RegisterServerEvent("esx_misc:syncPtfxEffecte")
AddEventHandler('esx_misc:syncPtfxEffecte', function(NId)
    TriggerClientEvent("esx_misc:syncPtfxEffect_cl", -1, source)
end)]]

----------------------
---Auto Gifts START---
----------------------

local GiftMoney = 5000
local GiftTime = 59


local AutoGift = {}

-- RegisterServerEvent("esx_misc:StartAutoGift")
-- AddEventHandler('esx_misc:StartAutoGift', function()
--     local xPlayer = ESX.GetPlayerFromId(source)
--     local Playeridentifier = xPlayer.identifier

--     if xPlayer then
--         MySQL.Async.fetchAll(
--             'SELECT * FROM users WHERE identifier = @identifier',
--             {
--                 ['@identifier'] = Playeridentifier,
--             }, function(result)

-- 				if not result[1].repairgift then

--                 MySQL.Async.fetchAll("UPDATE users SET repairgift = @repairgift WHERE identifier = @identifier",
--                 {
--                     ['@identifier'] = Playeridentifier,
--                     ['@repairgift'] = 1
--                 }
--                 )

--                 xPlayer.addAccountMoney("bank", 250000)
--                 TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', xPlayer.source, 'add', 5000, ' تسليم باقة: تعويض صيانة')
-- 				--TriggerClientEvent('chatMessage', -1,  { 128, 0, 0 }, 'تم تسليم  ^3 '..xPlayer.getName().." تعويض شنطة المركبة")
-- 				TriggerClientEvent('chatMessage', -1, " 📦 متجر نجم أونلاين " ,  { 186, 82, 17 } , " تم تسليم ^3"..xPlayer.getName().." ^7 باقة ".."^3 تعويض صيانة")

--             end
--         end)
--     end
    
-- end)

function DiscordLogReward(name, title, message, color)
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "سجل المكافأة التلقائية", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest('https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj', function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

-----------
---Other---
-- -----------
-- AddEventHandler("playerDropped", function()
-- 	local xPlayer = ESX.GetPlayerFromId(source)

-- 	if xPlayer then

-- 		playersOnline = playersOnline - 1

--         if #PlayersBlips > 0 then


-- 			for i=1, #PlayersBlips, 1 do
			

--                 if PlayersBlips[i].source == source then

-- 					PlayersBlips[i].source = nil

-- 					PlayersBlips[i].job = nil
                    
-- 					break
-- 				end

-- 			end


-- 		end

-- 	end


-- end)

-- AddEventHandler('esx:setJob', function(playerID, job, lastJob) -- on player set job

-- 	if #PlayersBlips > 0 then


-- 		for i=1, #PlayersBlips, 1 do
		

-- 			if PlayersBlips[i].source == source then


-- 				PlayersBlips[i].job = job.name
            
-- 				break
-- 			end

-- 		end


-- 	end

-- end)


RegisterServerEvent('esx_misc:GetCache')
AddEventHandler('esx_misc:GetCache', function()
    local xPlayer = ESX.GetPlayerFromId(source)


	-- table.insert(PlayersBlips, {
	-- 	source = source,
	-- 	job = xPlayer.job.name
	-- })
	--playersOnline = playersOnline + 1


    TriggerClientEvent("esx_misc:currentPortNum", source, currentPort)
	
	
	local ND = NoCrimetime_Data
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'NoCrimetime', ND['NoCrimetime'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'NoCrimetimePoleto', ND['NoCrimetimePoleto'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'NoCrimetimeSandy', ND['NoCrimetimeSandy'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'NoCrimetimeCayo', ND['NoCrimetimeCayo'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'NoCrimetimeLos', ND['NoCrimetimeLos'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'NewScenario', ND['NewScenario'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'StartRobbery', ND['StartRobbery'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'MainBank', ND['MainBank'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'SellDrugs', ND['SellDrugs'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'SmallBanks', ND['SmallBanks'].state)
	TriggerClientEvent('esx_misc:updatePromotionStatus', source, 'Stores', ND['Stores'].state)
	TriggerClientEvent('esx_misc:updateNoCrimetime', source, NoCrimetime)
end)

RegisterServerEvent('esx_misc:updateCache')
AddEventHandler('esx_misc:updateCache', function(data1, data2)
	local xPlayer = ESX.GetPlayerFromId(source)
	  
	  --TriggerClientEvent('esx_misc:updateStatus', source, alarm)
end)

function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = "",
        fivem = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
		elseif string.find(id, "fivem") then
            identifiers.fivem = id
        end
    end

    return identifiers
end

-------------------------
----tebex store START----
-------------------------
function TebexDiscordLog(name, title, message, color)
	local DiscordWebHook = ""
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "متجر نجم",
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1003983267957063752/1064230053992480819/33s.png"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

RegisterCommand('store', function(source, args, user)
    local targetid = args[1]
    local productName = args[2]
    local productstatus = args[3]
    local vehicle = args[4]
    local steam22 = args[5]
	local xTarget = ESX.GetPlayerFromId(targetid)
	local SteamName = GetPlayerName(targetid)
	
	local ids = ExtractIdentifiers(xTarget.source)
	_fivemID ="\n**fivem ID:  ** " ..ids.license..""
	_steamID ="\n**Steam ID:  ** " ..ids.steam..""
	steam_store = steam22
	_discordID ="\n<@" ..ids.discord:gsub("discord:", "")..">"
	if source == 0 then
	if xTarget then
	if productstatus == 'give' then
	TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
	if Config.product[productName].rewardMoney ~= nil then
		xTarget.addAccountMoney("bank", Config.product[productName].rewardMoney)
	end
	if Config.product[productName].rewardBlackMoney ~= nil then
		xTarget.addAccountMoney("black_money", Config.product[productName].rewardBlackMoney)
	end
	if Config.product[productName].doubleXP_Store  then
		TriggerEvent("zahya_xplevel:GiveStoreDoubleXP", xTarget.source, Config.product[productName].doubleXP_Store,productName)
	end
	TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', xTarget.source, 'add', 0, 'المتجر تسليم باقة: '..Config.product[productName].label)
	--TriggerClientEvent('esx_giveownedcar:spawnVehicle', targetid, targetid, Config.product[productName].cars.model, Config.product[productName].cars.label, 'none', 'player', Config.product[productName].cars.type)
	if Config.product[productName].cars ~= nil then
		for k,v in pairs(Config.product[productName].cars) do
			local generatedPlate = GeneratePlate()
			MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, type, stored, priceold, category, levelold, trunkkg, higherprice, lowerprice, name) VALUES (@owner, @plate, @vehicle, @type, @stored, @priceold, @category, @levelold, @trunkkg, @higherprice, @lowerprice, @name)', {
				['@owner'] = xTarget.identifier,
				['@plate'] = generatedPlate,
				['@vehicle'] = json.encode({model = GetHashKey(v.model), plate = generatedPlate, engineHealth = 1000.0, bodyHealth = 1000.0}),
				['@type'] = v.type,
				['@stored'] = 1,
				['@priceold'] = v.priceold,
				['@category'] = v.category,
				['@levelold'] = v.levelold,
				['@trunkkg'] = v.trunkkg,
				['@higherprice'] = v.higherprice,
				['@lowerprice'] = v.lowerprice,
				['@name'] = v.label,
			}, function(rowsChanged)
			end)
		end
	end
	---------add on license
	if Config.product[productName].endTime ~= nil then
		local finishdate = os.time() + (Config.product[productName].endTime*86400)
		MySQL.Async.execute('INSERT INTO user_licenses (type, owner, time) VALUES (@type, @owner, @time)', {
			['@type'] = productName,
			['@owner'] = xTarget.identifier,
			['@time'] = finishdate,
		}, function(rowsChanged)
		end)
	end
	-- التسجيل في السجل
	if Config.product[productName].registerInRecord then
	if Config.product[productName].rewardMoney ~= nil then
	rewardMoneyXtra = 'مبلغ: '..Config.product[productName].rewardMoney
	else
	rewardMoneyXtra = ''
	end
	if Config.product[productName].rewardBlackMoney ~= nil then
	rewardBlackMoneyXtra = 'أموال حمراء: '..Config.product[productName].rewardBlackMoney
	else
	rewardBlackMoneyXtra = ''
	end
	if Config.product[productName].rewardXp ~= nil then
	rewardXPXtra = 'خبرة: '..Config.product[productName].rewardXp
	else
	rewardXPXtra = ''
	end
	TriggerEvent('esx_qalle_brottsregister:add_TebexStore', xTarget.source, Config.product[productName].label..' '..rewardMoneyXtra..' '..rewardBlackMoneyXtra..' '..rewardXPXtra..'')
	end
	--
	TriggerClientEvent('chatMessage', -1, " 📦 متجر نجم أونلاين " ,  { 186, 82, 17 } ,  "تسليم ^3"..xTarget.getName().."^0 باقة ^3"..Config.product[productName].label)
	TebexDiscordLog(('متجر نجم'), 'تسليم '..Config.product[productName].label, 'هوية اللاعب: `'..xTarget.getName()..'`\n'.._discordID..'\n'.._steamID..'\n'.._fivemID..'', '********')
	elseif productstatus == 'expire' then
	TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
	elseif productstatus == 'take' then
	xTarget.removeAccountMoney("bank", Config.product[productName].rewardMoney)
    xTarget.removeAccountMoney("black_money", Config.product[productName].rewardBlackMoney)
	TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', xTarget.source, 'remove', Config.product[productName].rewardXp)
	TriggerEvent('esx_qalle_brottsregister:add_TebexStore', xTarget.source, Config.product[productName].label..' - حجز مشتريات ⛔')
	--TriggerClientEvent('chatMessage', -1, " 📦 متجر نجم  " ,  { 255, 191, 0 } ,  "^1حجز مشتريات ^3"..xTarget.getName().."^0 "..Config.product[productName].label)
	TriggerClientEvent('chatMessage', -1, " 📦 متجر نجم أونلاين " ,  { 186, 82, 17 } ,  "^1حجز مشتريات ^3"..xTarget.getName().."^0 ")
	end
	else
	TebexDiscordLog(('متجر نجم'), 'خطأ بتسليم '..Config.product[productName].label, 'اللاعب لم يكن متصل\n ستيم من المتجر: `'..steam_store..'`', '********')
	PerformHttpRequest("", function(err, text, headers) end, 'POST', json.encode({ username = 'متجر نجم', content = '@everyone !'}), { ['Content-Type'] = 'application/json' })
	end
	else
	TriggerClientEvent('esx:showNotification', source, '<font color=red>هل تعتقد يمكنك إعطاء نفسك باقة من المتجر عن طريق هاذه الكوماند؟</font>')
	--TriggerClientEvent('esx:showNotification', source, Config.product['t'].cars.label)
	end
	-- print("chaka")
	-- print(productName)
	if productName == 'eid2023' then
		--print("chaka")
		if Config.product["sponserSilver"].endTime ~= nil then
			local finishdate = os.time() + (Config.product["sponserSilver"].endTime*86400)
			MySQL.Async.execute('INSERT INTO user_licenses (type, owner, time) VALUES (@type, @owner, @time)', {
				['@type'] = 'sponserSilver',
				['@owner'] = xTarget.identifier,
				['@time'] = finishdate,
			}, function(rowsChanged)
			end)
			-- TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, 'eid2023', productstatus)
		end
		-- ExecuteCommand('store '..targetid..' sponserSilver give 0')
	end
end, false, {help = ("give product to your self")})
-- sponserBronze
-------------------------
----tebex store END------
-------------------------

-- أشياء مالي خلق اعدلها الحين

function MinaStatus (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "حالة الميناء", 
            ["icon_url"] = ""},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest("https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj", function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function MinaPlace (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "موقع التصدير"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function MinaStatusLog (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "موقع التصدير"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function MinaPlaceLog (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "موقع التصدير"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function license_revoke (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "سحب الرخص"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function killkickfreeze (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "طرد / تجميد/ قتل"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function killkickfreeze (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "طرد / تجميد/ قتل"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function f6revive (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "انعاش لاعب عبر f6"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function gotobring (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "أنتقال / سحب لاعب"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function f6kickallLog (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "طرد جميع الاعبين"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end


function logme1 (name, title, message, color) -- لوق ضعف الاجر
	local DiscordWebHook = "1304118500713041940" -- سيرفر الوقات
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "{zahya REGION} LOGS"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end


RegisterServerEvent('Mina:mg8i92aadminjob:kickallf6') -- لوق طرد جميع الاعبين
AddEventHandler('Mina:mg8i92aadminjob:kickallf6', function(name, title, message, color)
    f6kickallLog(name, title, message, color)
end)

RegisterServerEvent('Mina:8adoji2adminjob:killkickfreeze') -- لوج قتل تجميد طرد رقابة
AddEventHandler('Mina:8adoji2adminjob:killkickfreeze', function(name, title, message, color)
    killkickfreeze(name, title, message, color)
end)

RegisterServerEvent('Mina:ol2349oadminjob:gotobring') --
AddEventHandler('Mina:ol2349oadminjob:gotobring', function(name, title, message, color)
    gotobring(name, title, message, color)
end)

RegisterServerEvent('Mina:lad97ygadminjob:f6revive')
AddEventHandler('Mina:lad97ygadminjob:f6revive', function(name, title, message, color)
    f6revive(name, title, message, color)
end)

RegisterServerEvent('Mina:dlpayLogadj38')
AddEventHandler('Mina:dlpayLogadj38', function(name, title, message, color)
    logme1(name, title, message, color)
end)

RegisterServerEvent('Mina:msg')
AddEventHandler('Mina:msg', function(name, title, message, color)
    MinaStatus(name, title, message, color)
end)

RegisterServerEvent('MinaPlace:msg')
AddEventHandler('MinaPlace:msg', function(name, title, message, color)
    MinaPlace(name, title, message, color)
end)

RegisterServerEvent('MinaStatusLog:msg')
AddEventHandler('MinaStatusLog:msg', function(name, title, message, color)
    MinaStatusLog(name, title, message, color)
end)

RegisterServerEvent('MinaPlaceLog:msg')
AddEventHandler('MinaPlaceLog:msg', function(name, title, message, color)
    MinaPlaceLog(name, title, message, color)
end)

RegisterServerEvent('license_revokeLog:msg')
AddEventHandler('license_revokeLog:msg', function(name, title, message, color) -- license_revoke
    license_revoke(name, title, message, color)
end)



----------------hamada blips----------------
------------if you steal it you mom is bitch <3 ----------------




--------------blips-------------

local function UpdateHamadaBlips()
    local dutyPlayersPolice = {}
    local dutyPlayers = {}
    local policeJobs = { police = true, agent = true }
    local otherJobs = { ambulance = true, mechanic = true }

    for _, v in pairs(ESX.GetPlayers()) do
        local xPlayer = ESX.GetPlayerFromId(v)
        if xPlayer and xPlayer.job then
            local jobName = xPlayer.job.name
            if policeJobs[jobName] or otherJobs[jobName] then
                local ped = GetPlayerPed(v)
                local coords = GetEntityCoords(ped)
                local heading = GetEntityHeading(ped)
                local playerData = {
                    source = v,
                    job = jobName,
                    location = { x = coords.x, y = coords.y, z = coords.z, w = heading }
                }
                if policeJobs[jobName] then
                    table.insert(dutyPlayersPolice, playerData)
                else
                    table.insert(dutyPlayers, playerData)
                end
            end
        end
    end

    TriggerClientEvent("hamada:client:UpdateBlips", -1, dutyPlayersPolice, dutyPlayers)
end

CreateThread(function()
    while true do
        Wait(10000)
        UpdateHamadaBlips()
    end
end)






ESX.RegisterServerCallback('esx_misc:getSponserRoles', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	local roles = {}
	local is_have_any_role = false
	MySQL.Async.fetchAll('SELECT * FROM user_licenses WHERE owner = @identifier', {['@identifier'] = xPlayer.identifier},
	function (licenses)
		if type ~= nil then
			--local finishdate = tonumber(os.time())
			--print(finishdate)
			for i=1, #licenses, 1 do
				if licenses[i].type == 'sponserBronze' then -- and  licenses[i].time > finishdate
					table.insert(roles, {name = 'sponserbronze', label = "🥉 راعي برونزي"})
					is_have_any_role = true
				elseif licenses[i].type == 'sponserSilver' then -- and licenses[i].time > finishdate
					table.insert(roles, {name = 'sponsersilver', label = "🥈 راعي فضي"})
					is_have_any_role = true
				elseif licenses[i].type == 'sponserGold' then -- and licenses[i].time > finishdate
					table.insert(roles, {name = 'sponsergold', label = "🏅 راعي ذهبي"})
					is_have_any_role = true
				elseif licenses[i].type == 'sponserPlatinum' then -- and licenses[i].time > finishdate
					table.insert(roles, {name = 'sponserplat', label = "⚡ راعي بلاتيني"})
					is_have_any_role = true
				elseif licenses[i].type == 'sponserDiamond' then -- and licenses[i].time > finishdate
					table.insert(roles, {name = 'sponserdiamond', label = "💎 راعي الماسي"})
					is_have_any_role = true
				elseif licenses[i].type == 'sponserOfficial' then -- and licenses[i].time > finishdate
					table.insert(roles, {name = 'sponserofficial', label = "🏆 راعي  رسمي"})
					is_have_any_role = true
				elseif licenses[i].type == 'sponserstrategic' then -- and licenses[i].time > finishdate
					table.insert(roles, {name = 'sponserstart', label = "💠 راعي استراتيجي"})
					is_have_any_role = true
				end	
				-- if type == 'driver' then
				-- 	if licenses[i].type == 'drive' or licenses[i].type == 'drive_bike' or licenses[i].type == 'drive_truck' then
				-- 		show = true
				-- 	end	
				-- elseif type =='market' then
				-- 	if licenses[i].type == 'market' then
				-- 		show = true
				-- 	end							
				-- elseif type =='weapon' then
				-- 	if licenses[i].type == 'weapon' then
				-- 		show = true
				-- 	end
				-- elseif type == 'sponser' then
				-- 	if licenses[i].type == 'sponser' then
				-- 		show = true
				-- 	end
				-- end
			end
			cb(is_have_any_role, roles, xPlayer.getName())
		else
			cb(false)
		end
	end)
end)



RegisterServerEvent("esx_misc:NoCrimetimeDrugs")
AddEventHandler('esx_misc:NoCrimetimeDrugs', function(isToggle, player)
	local source = player
    local xPlayer = ESX.GetPlayerFromId(source)
    local promotionName = 'SellDrugs'
	local min
	if isToggle == false then
		min = 0
	end
	if xPlayer.job.name == 'admin' then
		if isToggle then
			print(('esx_misc: ^3%s^0 Has Toggled '..promotionName):format(GetPlayerName(source)))
			if NoCrimetime_Data[promotionName].state then
			TriggerClientEvent('esx_misc:watermark_promotion', -1, promotionName, false, 2)
			NoCrimetime_Data[promotionName].state = false
			
			if #NoCrimetime > 0 then
						for i=1, #NoCrimetime, 1 do
							if NoCrimetime[i].name == promotionName then
								NoCrimetime[i].active = false
							end
						end
					end
					TriggerClientEvent('esx_misc:updateNoCrimetime', -1, NoCrimetime)
			else
			TriggerClientEvent('esx_misc:watermark_promotion', -1, promotionName, true, 2)
			NoCrimetime_Data[promotionName].state = true
			table.insert(NoCrimetime, {name = promotionName, label = NoCrimetime_Data[promotionName].label, active=true})
			TriggerClientEvent('esx_misc:updateNoCrimetime', -1, NoCrimetime)
			end
			
			elseif not isToggle then
			NoCrimetime_Data[promotionName].min = min
			NoCrimetime_Data[promotionName].sec = 0
			
			print(('esx_misc: ^3%s^0 Has rest the time to ^1%s^0 min for %s'):format(GetPlayerName(source),min,promotionName))
			TriggerClientEvent('esx_misc:watermark_promotion', -1, promotionName, false, 2)
			NoCrimetime_Data[promotionName].state = false
			
			if min > 0 and not NoCrimetime_Data[promotionName].timer then
			timing(promotionName)
			end
		end
	else
	print(('esx_misc: %s attempted to use esx_misc:NoCrimetime (not adminjob!)!'):format(xPlayer.identifier))
	end
end)

RegisterNetEvent('tebexstore:bkgat')
AddEventHandler('tebexstore:bkgat', function(productNamea, productstatuss)
	local targetid = source
    local xTarget = ESX.GetPlayerFromId(targetid)
    local productName = productNamea
    local productstatus = productstatuss
    -- local SteamName = GetPlayerName(targetid)
    local timer_tebex = nil
    local is_timer = false

    if xTarget then
        local product = Config.product[productName]
        
        if productstatus == 'give' then
            xTarget.addAccountMoney("bank", product.rewardMoney)
            xTarget.addAccountMoney("black_money", product.rewardBlackMoney)
            TriggerEvent('SystemXpLevel:updateCurrentPlayerXP', xTarget.source, 'add', product.rewardXp, 'المتجر تسليم باقة: ' .. product.label)
            TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)

            if product.registerInRecord then
                if product.rewardMoney >= 1 then
                    rewardMoneyXtra = 'مبلغ: ' .. product.rewardMoney
                else
                    rewardMoneyXtra = ''
                end
                
                if product.timer then
                    timer_tebex = product.timer
                    is_timer = true
                end
                
                if product.rewardBlackMoney >= 1 then
                    rewardBlackMoneyXtra = 'أموال حمراء: ' .. product.rewardBlackMoney
                else
                    rewardBlackMoneyXtra = ''
                end
                
                if product.rewardXp >= 1 then
                    rewardXPXtra = 'خبرة: ' .. product.rewardXp
                    TriggerEvent('SystemXpLevel:updateCurrentPlayerXP', xTarget.source, 'addnoduble', product.rewardXp, product.label, 'AbdurhmanOnTop')
                else
                    rewardXPXtra = ''
                end
                
                if product.ra3i then
                    MySQL.Async.execute('INSERT INTO arab_island_ra3i (identifier, name, time, number) VALUES (@identifier, @name, @time, @number)',
                    {
                        ['@identifier'] = xTarget.identifier,
                        ['@name'] = product.name,
                        ['@time'] = product.timer,
                        ['@number'] = productName
                    })
                end
                
                if product.car then
                    TriggerClientEvent("esx_giveownedcar:spawnVehicle2", xTarget.source, xTarget.source, product.model, product.carmodel, product.label, xTarget.getName(), "console")
                end
                
                TriggerEvent('esx_qalle_brottsregister:add_TebexStore', xTarget.source, product.label .. ' ' .. rewardMoneyXtra .. ' ' .. rewardBlackMoneyXtra .. ' ' .. rewardXPXtra)
                
                if is_timer then
                    MySQL.Async.execute('INSERT INTO arabisland (identifier, timer) VALUES (@identifier, @timer)',
                    {
                        ['@identifier'] = xTarget.identifier,
                        ['@timer'] = timer_tebex
                    })
                    TriggerEvent('SystemXpLevel:DoubleXpLevelStore', xTarget.source, "start")
                    TriggerClientEvent('esx_misc:watermark_promotion', xTarget.source, "redx_store", true, 2)
                    TriggerClientEvent("esx_misc:updatePromotionTimer", xTarget.source, "redx_store", true, timer_tebex)
                    TriggerClientEvent('esx_misc:updateNoCrimetime', xTarget.source, "redx_store")
                    is_player_have_double_xp_from_store[xTarget.identifier] = {status = true, time = timer_tebex}
                    timer_player_in_store[xTarget.identifier] = {status = true, time = timer_tebex, id = xTarget.source}
                end
            end
            TriggerClientEvent('chatMessage', -1, " متجر مقاطعة نجم 🛒 ", {255, 191, 0}, "تسليم ^3" .. xTarget.getName() .. "^0  " .. product.label)
           
			MySQL.Async.execute('INSERT INTO user_licenses (type, owner, time) VALUES (@type, @owner, @time)', { ['@type']  = productName, ['@owner'] = xTarget.identifier, ['@time'] = timer_tebex })
			
			PerformHttpRequest("https://discord.com/api/webhooks/1331581191152861257/YnHyszC9TpUyMEV88fEsC-tMG4Ig_3R9668047yd4UR7OsFa9q3uTqMdHpd5IjresVsO", function(err, text, headers) end, 'POST', json.encode({ username = "لوج الرواعي",embeds = {
				{
					type ="rich",
					title = "تسليم باقة راعي",
					color = 2883328,
					fields = {
					  {
						name = "هوية الاعب: ",
						value = xTarget.getName() .. " | " .. xTarget.identifier,
						inline = true
					  },
					  {
						name = "الادمن:",
						value = ESX.GetPlayerFromId(source).getName() .. " | " .. ESX.GetPlayerFromId(source).identifier,
						inline = true
					  },
					  {
						name = "اسم الباقة:",
						value = product.label,
						inline = true
					  },
					  {
						name = "خبرة:",
						value = product.rewardXp.." XP",
						inline = true
					  },
					  {
						name = "مبلغ الكاش:",
						value = product.rewardMoney.."$",
						inline = true
					  },
					  {
						name = "مبلغ الاموال القذرة",
						value = product.rewardBlackMoney.."$",
						inline = true
					  }
					},
					["footer"] =  { 
						["text"]= "متجر نجم",
						["icon_url"] = "https://cdn.discordapp.com/attachments/1003983267957063752/1064230053992480819/33s.png"
					},
				}
			}}), { ['Content-Type'] = 'application/json' })
        
        elseif productstatus == 'expire' then
            TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
        
        elseif productstatus == "remove" then
            TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
            TriggerEvent("esx_qalle_brottsregister:remove_TebexStore_ra3i", productName)
        
        elseif productstatus == 'take' then
            xTarget.removeAccountMoney("bank", product.rewardMoney)
            xTarget.removeAccountMoney("black_money", product.rewardBlackMoney)
            TriggerEvent('esx_xp:removeXP', xTarget.source, product.rewardXp)
            TriggerEvent('esx_qalle_brottsregister:add_TebexStore', xTarget.source, product.label .. ' - حجز مشتريات ⛔')
            -- TriggerClientEvent('chatMessage', -1, " 🛒 متجر مقاطعة نجم  ", {255, 191, 0}, "^1حجز مشتريات ^3" .. xTarget.getName() .. "^0 ")
        end
    else
        PerformHttpRequest("", function(err, text, headers) end, 'POST', json.encode({username = 'متجر مقاطعة نجم', content = '@everyone !'}), {['Content-Type'] = 'application/json'})
    end
end)


RegisterCommand('store', function(source, args, user)
    local targetid = args[1]
    local productName = args[2]
    local productstatus = args[3]
    local steam22 = args[4]
	local xTarget = ESX.GetPlayerFromId(targetid)
	local SteamName = GetPlayerName(targetid)
	local timer_tebex = nil
	local is_timer = false
	if xTarget then
		if productstatus == 'give' then
			xTarget.addAccountMoney("bank", Config.product[productName].rewardMoney)
			xTarget.addAccountMoney("black_money", Config.product[productName].rewardBlackMoney)
			TriggerEvent('SystemXpLevel:updateCurrentPlayerXP', xTarget.source, 'add', Config.product[productName].rewardXp, 'المتجر تسليم باقة: '..Config.product[productName].label)
			TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
			if Config.product[productName].registerInRecord then
				if Config.product[productName].rewardMoney >= 1 then
					rewardMoneyXtra = 'مبلغ: '..Config.product[productName].rewardMoney
					xTarget.addAccountMoney("bank", Config.product[productName].rewardMoney)
				else
					rewardMoneyXtra = ''
				end
				if Config.product[productName].timer then
					timer_tebex = Config.product[productName].timer
					is_timer = true
				end
				if Config.product[productName].rewardBlackMoney >= 1 then
					rewardBlackMoneyXtra = 'أموال حمراء: '..Config.product[productName].rewardBlackMoney
					xTarget.addAccountMoney("black_money", Config.product[productName].rewardBlackMoney)
				else
					rewardBlackMoneyXtra = ''
				end
				if Config.product[productName].rewardXp >= 1 then
					rewardXPXtra = 'خبرة: '..Config.product[productName].rewardXp
					TriggerEvent('SystemXpLevel:updateCurrentPlayerXP', xTarget.source, 'addnoduble', Config.product[productName].rewardXp, Config.product[productName].label, 'AbdurhmanOnTop')
				else
					rewardXPXtra = ''
				end
				if Config.product[productName].ra3i then
					MySQL.Async.execute('INSERT INTO island_ra3i (identifier, name, time, number) VALUES (@identifier, @name, @time, @number)',
					{
						['@identifier'] = xTarget.identifier,
						['@name'] = Config.product[productName].name,
						['@time'] = Config.product[productName].timer,
						['@number'] = productName
					})
				end
				if Config.product[productName].car then
					TriggerClientEvent("esx_giveownedcar:spawnVehicle2", xTarget.source, xTarget.source, Config.product[productName].model, Config.product[productName].carmodel, Config.product[productName].label, xTarget.getName(), "console")
				end
				TriggerEvent('esx_qalle_brottsregister:add_TebexStore', xTarget.source, Config.product[productName].label..' '..rewardMoneyXtra..' '..rewardBlackMoneyXtra..' '..rewardXPXtra..'')
				if is_timer then
					MySQL.Async.execute('INSERT INTO arabisland (identifier, timer) VALUES (@identifier, @timer)',
					{
						['@identifier'] = xTarget.identifier,
						['@timer'] = timer_tebex
					})
					TriggerEvent('SystemXpLevel:DoubleXpLevelStore', xTarget.source, "start")
					TriggerClientEvent('esx_misc:watermark_promotion', xTarget.source, "redx_store", true, 2)
					TriggerClientEvent("esx_misc:updatePromotionTimer", xTarget.source, "redx_store", true, timer_tebex)
					TriggerClientEvent('esx_misc:updateNoCrimetime', xTarget.source, "redx_store")
					is_player_have_double_xp_from_store[xTarget.identifier] = {status = true, time = timer_tebex}
					timer_player_in_store[xTarget.identifier] = {status = true, time = timer_tebex, id = xTarget.source}
				end
			end
			TriggerClientEvent('chatMessage', -1, " متجر مقاطعة نجم 🛒 " ,  { 255, 191, 0 } ,  "تسليم ^3"..xTarget.getName().."^0  "..Config.product[productName].label)
			TebexDiscordLog(('متجر مقاطعة نجم'), 'تسليم '..Config.product[productName].label, 'هوية اللاعب: `'..xTarget.getName()..'`\n\n'..xTarget.identifier..'', '********')
		elseif productstatus == 'expire' then
			TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
		elseif productstatus == "remove" then
			TriggerClientEvent('esx_misc:tebexStoreScaleform', xTarget.source, productName, productstatus)
			TriggerEvent("esx_qalle_brottsregister:remove_TebexStore_ra3i", productName)
		elseif productstatus == 'take' then
			xTarget.removeAccountMoney("bank", Config.product[productName].rewardMoney)
			xTarget.removeAccountMoney("black_money", Config.product[productName].rewardBlackMoney)
			TriggerEvent('esx_xp:removeXP', xTarget.source, Config.product[productName].rewardXp)
			TriggerEvent('esx_qalle_brottsregister:add_TebexStore', xTarget.source, Config.product[productName].label..' - حجز مشتريات ⛔')
			--TriggerClientEvent('chatMessage', -1, " 🛒 متجر مقاطعة نجم  " ,  { 255, 191, 0 } ,  "^1حجز مشتريات ^3"..xTarget.getName().."^0 "..Config.product[productName].label)
			TriggerClientEvent('chatMessage', -1, " 🛒 متجر مقاطعة نجم  " ,  { 255, 191, 0 } ,  "^1حجز مشتريات ^3"..xTarget.getName().."^0 ")
		end
	else
		-- TebexDiscordLog(('متجر منطقة مقاطعة نجم'), 'خطأ بتسليم '..Config.product[productName].label, 'اللاعب لم يكن متصل\n ستيم من المتجر: `'..SteamName..'`', '********')
		PerformHttpRequest("", function(err, text, headers) end, 'POST', json.encode({ username = 'متجر مقاطعة نجم', content = '@everyone !'}), { ['Content-Type'] = 'application/json' })
	end
end, true, {help = 'tebex store', validate = true, arguments = {
	{name = 'targetid', help = 'targetid', type = 'player'},
	{name = 'productName', help = 'productName', type = 'string'},
	{name = 'productstatus', help = 'productstatus | give/take/expire', type = 'string'}
}})

ESX.RegisterUsableItem('handcuff_key', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)

    -- البحث عن أقرب لاعب
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()

    if closestPlayer ~= -1 and closestDistance <= 2.0 then
        TriggerClientEvent('checkIfHandcuffed', closestPlayer, function(isHandcuffed)
            if isHandcuffed then
                TriggerClientEvent('pogressBar:drawBar', source, 5000, "جاري فتح الكلبشات...")

                Citizen.SetTimeout(5000, function()
                    TriggerClientEvent('esx_misc:handcuff', closestPlayer)
                    xPlayer.removeInventoryItem('handcuff_key', 1)
                    TriggerClientEvent('esx:showNotification', source, '✅ تم فك القيد عن اللاعب')
                end)
            else
                TriggerClientEvent('esx:showNotification', source, '❌ هذا اللاعب غير مكلبش')
            end
        end)
    else
        TriggerClientEvent('esx:showNotification', source, '❌ لا يوجد لاعب قريب منك')
    end
end)


