-- Performance Configuration for ESX Misc Modules
-- Adjust these values to balance performance vs functionality

PerformanceConfig = {}

-- Client-side performance settings
PerformanceConfig.Client = {
    -- Main loop intervals (in milliseconds)
    panicButtonMainLoop = 100,        -- Main panic button check interval (was 1ms)
    panicButtonAlarmCheck = 250,      -- Alarm status check interval (was every frame)
    panicButtonNoCrimeCheck = 500,    -- No-crime status check interval (was every frame)
    panicButtonGodmodCheck = 500,     -- Godmode check interval (was every frame)
    panicButtonXPCheck = 1000,        -- XP zone check interval (was every frame)
    panicButtonDrawTextCheck = 500,   -- Draw text check interval (was every frame)
    panicButtonBlipCheck = 300,       -- Blip check interval (was every frame)
    panicButtonF10Check = 50,         -- F10 help system check interval (was 0ms)
    panicButtonF10Draw = 50,          -- F10 counter draw interval (was 2ms)
    panicButtonF10Cooldown = 100,     -- F10 cooldown check interval (was 0ms)
    
    -- Vehicle density control
    vehicleDensityControl = 100,      -- Vehicle density natives interval (was 0ms)
    vehicleAreaClear = 5000,          -- Area clearing interval (was every frame)
    
    -- Godmode intervals
    godmodeMainLoop = 500,            -- Main godmode loop interval
    godmodeNoCrimeLoop = 500,         -- No-crime godmode loop interval
    
    -- Performance monitoring
    enablePerformanceMonitoring = false, -- Enable/disable performance monitoring
    performanceReportInterval = 30000,   -- Performance report interval (30 seconds)
}

-- Server-side performance settings
PerformanceConfig.Server = {
    -- Panic system intervals
    panicTimerUpdate = 60000,         -- Panic timer update interval (1 minute)
    panicStatusSync = 120000,         -- Status sync interval (2 minutes, was 1 minute)
    
    -- Database operation intervals
    vehicleCleanupInterval = 300000,  -- Vehicle cleanup interval (5 minutes, was 1 minute)
    vehicleCleanupBatchSize = 50,     -- Max vehicles to process per batch
    
    -- Event throttling
    updatePanicThrottle = true,       -- Enable throttling for UpdatePanic events
    updatePanicMinInterval = 1000,    -- Minimum interval between UpdatePanic events (1 second)
}

-- Performance optimization flags
PerformanceConfig.Optimizations = {
    -- Client optimizations
    useTimerBasedChecks = true,       -- Use timer-based checks instead of every-frame checks
    batchDatabaseOperations = true,   -- Batch database operations when possible
    enableNilChecks = true,           -- Add nil checks to prevent errors
    optimizeDrawCalls = true,         -- Optimize drawing operations
    
    -- Memory optimizations
    enableGarbageCollection = true,   -- Enable periodic garbage collection
    garbageCollectionInterval = 30000, -- Garbage collection interval (30 seconds)
    
    -- Network optimizations
    throttleClientEvents = true,      -- Throttle client events to prevent spam
    compressEventData = false,        -- Compress event data (experimental)
}

-- Debug and monitoring settings
PerformanceConfig.Debug = {
    enableDebugPrints = false,        -- Enable debug print statements
    enablePerformanceLogs = false,    -- Enable performance logging
    logSlowOperations = true,         -- Log operations that take longer than threshold
    slowOperationThreshold = 50,      -- Threshold for slow operations (ms)
    
    -- Memory monitoring
    enableMemoryMonitoring = false,   -- Enable memory usage monitoring
    memoryWarningThreshold = 50,      -- Memory warning threshold (MB)
    memoryCheckInterval = 10000,      -- Memory check interval (10 seconds)
}

-- Dynamic performance adjustment
PerformanceConfig.Dynamic = {
    enableDynamicAdjustment = false,  -- Enable dynamic performance adjustment
    targetFPS = 30,                   -- Target FPS for dynamic adjustment
    adjustmentInterval = 5000,        -- How often to check and adjust (5 seconds)
    
    -- Performance thresholds
    goodPerformanceThreshold = 40,    -- FPS above this is considered good
    poorPerformanceThreshold = 20,    -- FPS below this is considered poor
    
    -- Adjustment factors
    performanceAdjustmentFactor = 1.5, -- Multiply intervals by this when performance is poor
    recoveryAdjustmentFactor = 0.8,   -- Multiply intervals by this when performance recovers
}

-- Export the configuration
return PerformanceConfig
