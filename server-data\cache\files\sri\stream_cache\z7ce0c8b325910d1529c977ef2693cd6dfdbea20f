<?xml version="1.0" encoding="UTF-8"?>
<TextureDictionary>
 <Item>
  <Name>eagle_rs-a_p225</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, X1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="11" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>eagle_rs-a_p225.dds</FileName>
 </Item>
 <Item>
  <Name>livery_sign_4</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>livery_sign_4.dds</FileName>
 </Item>
 <Item>
  <Name>vehicle_generic_glasswindows3</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="11" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>vehicle_generic_glasswindows3.dds</FileName>
 </Item>
 <Item>
  <Name>equipment_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_A8R8G8B8</Format>
  <FileName>equipment_n.dds</FileName>
 </Item>
 <Item>
  <Name>dashboard_instruments</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>dashboard_instruments.dds</FileName>
 </Item>
 <Item>
  <Name>interior_lod0_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="9" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>interior_lod0_spec.dds</FileName>
 </Item>
 <Item>
  <Name>taillights_broken</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>taillights_broken.dds</FileName>
 </Item>
 <Item>
  <Name>buenolights2</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>buenolights2.dds</FileName>
 </Item>
 <Item>
  <Name>dirtmap</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, X1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="11" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>dirtmap.dds</FileName>
 </Item>
 <Item>
  <Name>devstore1</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>devstore1.dds</FileName>
 </Item>
 <Item>
  <Name>miscbolts</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>miscbolts.dds</FileName>
 </Item>
 <Item>
  <Name>equipment_s</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, X1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="11" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>equipment_s.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion_solo_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion_solo_spec.dds</FileName>
 </Item>
 <Item>
  <Name>xlp_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>xlp_n.dds</FileName>
 </Item>
 <Item>
  <Name>tyre_s</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>tyre_s.dds</FileName>
 </Item>
 <Item>
  <Name>script_rt_dials_gen_taxi</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>script_rt_dials_gen_taxi.dds</FileName>
 </Item>
 <Item>
  <Name>common_chassis_diffuse</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>common_chassis_diffuse.dds</FileName>
 </Item>
 <Item>
  <Name>setina_v1</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>setina_v1.dds</FileName>
 </Item>
 <Item>
  <Name>caprice_details_2_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>caprice_details_2_spec.dds</FileName>
 </Item>
 <Item>
  <Name>buenolights</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>buenolights.dds</FileName>
 </Item>
 <Item>
  <Name>dash_plastic_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>dash_plastic_n.dds</FileName>
 </Item>
 <Item>
  <Name>Caprice_detailing_lights</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>Caprice_detailing_lights.dds</FileName>
 </Item>
 <Item>
  <Name>plastic_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="16" />
  <Height value="16" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>plastic_spec.dds</FileName>
 </Item>
 <Item>
  <Name>xlp</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>xlp.dds</FileName>
 </Item>
 <Item>
  <Name>seats_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="128" />
  <Height value="128" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>seats_n.dds</FileName>
 </Item>
 <Item>
  <Name>seat_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="16" />
  <Height value="16" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>seat_spec.dds</FileName>
 </Item>
 <Item>
  <Name>taillights</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>taillights.dds</FileName>
 </Item>
 <Item>
  <Name>floor_tex</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>floor_tex.dds</FileName>
 </Item>
 <Item>
  <Name>Caprice_detailing_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>Caprice_detailing_n.dds</FileName>
 </Item>
 <Item>
  <Name>pushbar_black</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>pushbar_black.dds</FileName>
 </Item>
 <Item>
  <Name>xlp_s</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>xlp_s.dds</FileName>
 </Item>
 <Item>
  <Name>seat_leather</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X256, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>seat_leather.dds</FileName>
 </Item>
 <Item>
  <Name>taillights_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>taillights_n.dds</FileName>
 </Item>
 <Item>
  <Name>upper_dash_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="300" />
  <Height value="300" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>upper_dash_n.dds</FileName>
 </Item>
 <Item>
  <Name>miscbolts_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>miscbolts_n.dds</FileName>
 </Item>
 <Item>
  <Name>exhaust</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="9" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>exhaust.dds</FileName>
 </Item>
 <Item>
  <Name>interior_lod0</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>interior_lod0.dds</FileName>
 </Item>
 <Item>
  <Name>MSTAR_1</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>MSTAR_1.dds</FileName>
 </Item>
 <Item>
  <Name>equipment</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="11" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>equipment.dds</FileName>
 </Item>
 <Item>
  <Name>livery_sign_3</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>livery_sign_3.dds</FileName>
 </Item>
 <Item>
  <Name>interior_trim</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X256, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>interior_trim.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion_s</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion_s.dds</FileName>
 </Item>
 <Item>
  <Name>undercarriage</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>undercarriage.dds</FileName>
 </Item>
 <Item>
  <Name>body</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X256, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>body.dds</FileName>
 </Item>
 <Item>
  <Name>interior_L1</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>interior_L1.dds</FileName>
 </Item>
 <Item>
  <Name>interior_trim_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="16" />
  <Height value="16" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>interior_trim_spec.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion_solo_emis</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion_solo_emis.dds</FileName>
 </Item>
 <Item>
  <Name>caprice_details_2</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, X1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="11" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>caprice_details_2.dds</FileName>
 </Item>
 <Item>
  <Name>interior_emis</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>interior_emis.dds</FileName>
 </Item>
 <Item>
  <Name>caprice_details_2_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>caprice_details_2_n.dds</FileName>
 </Item>
 <Item>
  <Name>undercarriage_specular</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="9" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>undercarriage_specular.dds</FileName>
 </Item>
 <Item>
  <Name>roof_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="128" />
  <Height value="128" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>roof_n.dds</FileName>
 </Item>
 <Item>
  <Name>setina_v1_s</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="8" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>setina_v1_s.dds</FileName>
 </Item>
 <Item>
  <Name>taillight_emis</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X256, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>taillight_emis.dds</FileName>
 </Item>
 <Item>
  <Name>eagle_rs-a_p225_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>eagle_rs-a_p225_n.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion.dds</FileName>
 </Item>
 <Item>
  <Name>tail_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="4" />
  <Height value="4" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>tail_spec.dds</FileName>
 </Item>
 <Item>
  <Name>Caprice_detailing_s</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>Caprice_detailing_s.dds</FileName>
 </Item>
 <Item>
  <Name>roof</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="300" />
  <Height value="300" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>roof.dds</FileName>
 </Item>
 <Item>
  <Name>Caprice_detailing</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>Caprice_detailing.dds</FileName>
 </Item>
 <Item>
  <Name>common_chassis_diffuse_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="9" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>common_chassis_diffuse_n.dds</FileName>
 </Item>
 <Item>
  <Name>dash_plastic</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>dash_plastic.dds</FileName>
 </Item>
 <Item>
  <Name>dashboard_instruments_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>dashboard_instruments_spec.dds</FileName>
 </Item>
 <Item>
  <Name>upper_dash</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X128, X256, Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="300" />
  <Height value="300" />
  <MipLevels value="1" />
  <Format>D3DFMT_A8R8G8B8</Format>
  <FileName>upper_dash.dds</FileName>
 </Item>
 <Item>
  <Name>equipment_emis</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>equipment_emis.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion_n.dds</FileName>
 </Item>
 <Item>
  <Name>livery_sign_2</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>livery_sign_2.dds</FileName>
 </Item>
 <Item>
  <Name>undercarriage_n</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="9" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>undercarriage_n.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion_solo_normal</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion_solo_normal.dds</FileName>
 </Item>
 <Item>
  <Name>floor_spec</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="16" />
  <Height value="16" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>floor_spec.dds</FileName>
 </Item>
 <Item>
  <Name>livery_sign_1</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, Y64, X256, Y512, Y1024, Y2048, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="2048" />
  <Height value="2048" />
  <MipLevels value="10" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>livery_sign_1.dds</FileName>
 </Item>
 <Item>
  <Name>dashboard_instruments_emis</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y512, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="512" />
  <Height value="512" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>dashboard_instruments_emis.dds</FileName>
 </Item>
 <Item>
  <Name>whelen_ion_solo_diffuse</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>Y1024, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="1024" />
  <Height value="1024" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT5</Format>
  <FileName>whelen_ion_solo_diffuse.dds</FileName>
 </Item>
 <Item>
  <Name>common_chassis_diffuse_specular</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X32, X64, X128, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="256" />
  <Height value="256" />
  <MipLevels value="9" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>common_chassis_diffuse_specular.dds</FileName>
 </Item>
 <Item>
  <Name>seats</Name>
  <Unk32 value="128" />
  <Usage>DEFAULT</Usage>
  <UsageFlags>X64, UNK24</UsageFlags>
  <ExtraFlags value="0" />
  <Width value="128" />
  <Height value="128" />
  <MipLevels value="1" />
  <Format>D3DFMT_DXT1</Format>
  <FileName>seats.dds</FileName>
 </Item>
</TextureDictionary>
