--[[
#add this to database first
ALTER TABLE users ADD COLUMN xp INT DEFAULT 0;
#server side code to add and remove xp
TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'add', 500, 'السبب')
TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'remove', 500, 'السبب')

#client side code to add and remove xp
#Add 10
TriggerServerEvent('zahya_xplevel:updateCurrentPlayerXP_clientSide', 'add', 10, 'السبب')  

#remove 500
TriggerServerEvent('zahya_xplevel:updateCurrentPlayerXP_clientSide', 'remove', 500, 'السبب')


#my function to check level
function checkRequiredXPlevel(required, msg)
	local xp = exports.zahya_xplevel.ESXP_GetXP()
	local level = exports.zahya_xplevel.ESXP_GetRank()
	
	if level >= required then
		return true
	else
		ESX.ShowNotification(msg)
		
		return false
	end
end

local msg = '<font color=red>تحتاج إلى مستوى خبرة <font color=orange>'..Config.xp_level_required..' </br><font color=white>لشراء متجر'
if checkRequiredXPlevel(Config.xp_level_required,msg) then
	OpenShopCenter()
end

exports.zahya_xplevel:ESXP_Add(5)
exports.zahya_xplevel:ESXP_Remove(5)

السكربتات الموجود فيها باسوورد
esx_adminjob
esx_seaportjob
]]


local userDBUpdate = {}

if not ESX then
	TriggerEvent('esx:getSharedObjectp23Njd23byabd', function(obj) ESX = obj end)
end

local dubleXP = false

function dlxpmainlogsserver (name, title, message, color)
	local DiscordWebHook = "https://discord.com/api/webhooks/1309989888640155699/7QaVsNe9dCch5JGl-WPKxLbTNT-nqDJiV3_iiWR012jjD6IFlCqvzRNiJkJw9apdrsUD" -- سيرفر الوقات
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "سجل ضعف الخبرة"}
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end 



function GetPlayerLicense(id)
    local xPlayer = ESX.GetPlayerFromId(id)

    if xPlayer and xPlayer ~= nil then
        return xPlayer.identifier
    end

    return false
end

------
-- GetOnlinePlayers
--
-- @param playerId          - The player's id
-- @param players           - The list of players pulled from the DB
--
-- Fetches the online players from the list pulled form the DB
------
function GetOnlinePlayers(playerId, players)
    local Active = {}

    for _, playerId in ipairs(GetPlayers()) do
        local name = GetPlayerName(playerId)
        local license = GetPlayerLicense(playerId)

        for k, v in pairs(players) do
            if license == v.license or license == v.identifier then
                local Player = {
                    name = name,
                    id = playerId,
                    xp = v.rp_xp,
                    rank = v.rp_rank
                }

                -- Current player
                if GetPlayerLicense(playerId) == license then
                    Player.current = true
                end
                            
                if Config.Leaderboard.ShowPing then
                    Player.ping = GetPlayerPing(playerId)
                end
    
                table.insert(Active, Player)
                break
            end
        end
    end

    return Active 
end

------
-- UpdatePlayer
--
-- @param playerId          - The player's id
-- @param xp                - The player's current XP
-- @param rank              - The player's current rank
--
-- Fetches active players and initialises for current player
------
function FetchActivePlayers(playerId, xp, rank)
    MySQL.Async.fetchAll('SELECT * FROM users', {}, function(players)
        if #players > 0 then
            TriggerClientEvent("zahya_xplevel:InitialPlayerClientSide", playerId, xp, rank, GetOnlinePlayers(playerId, players))
        end
    end)
end

------
-- UpdatePlayer
--
-- @param playerId          - The player's id
-- @param xp                - The XP value to set
--
-- Updates the given user's XP
------
--[[
function UpdatePlayer(playerId, xp)
    local xPlayer = ESX.GetPlayerFromId(playerId)

    if xp == nil or not IsInt(xp) then
        TriggerClientEvent("esx_xp:print", playerId, _("err_type_check", "XP", "integer"))

        return
    end

    if xPlayer ~= nil then
        local goalXP = LimitXP(tonumber(xp))
        local goalRank = GetRankFromXP(goalXP)

        MySQL.Async.execute('UPDATE users SET rp_xp = @xp, rp_rank = @rank WHERE identifier = @identifier', {
            ['@identifier'] = xPlayer.identifier,
            ['@xp'] = goalXP,
            ['@rank'] = goalRank
        }, function(result)
            xPlayer.set("xp", goalXP)
            xPlayer.set("rank", goalRank)

            -- Update the player's XP bar
            xPlayer.triggerEvent("esx_xp:update", goalXP, goalRank)
        end)
    end
end]]

function UpdatePlayer(playerId)

    local xPlayer = ESX.GetPlayerFromId(playerId)


    if xPlayer ~= nil then
        
        if not userDBUpdate[playerId] then

            userDBUpdate[playerId] = true



            local xPlayer = ESX.GetPlayerFromId(playerId)

            if xPlayer ~= nil then
    
                local goalXP = tonumber(xPlayer.get("xp"))
                local goalRank = GetRankFromXP(goalXP)

                MySQL.Async.execute('UPDATE users SET rp_xp = @xp, rp_rank = @rank WHERE identifier = @identifier', {

                    ['@identifier'] = xPlayer.identifier,

                    ['@xp'] = goalXP,

                    ['@rank'] = goalRank

                }, function(result)
            
                    xPlayer.set("rank", goalRank)


                    -- Update the player's XP bar
                    xPlayer.triggerEvent("esx_xp:update", goalXP, goalRank)

                    userDBUpdate[playerId] = false
    
                end)

            end


        end

    end

end


------------------------------------------------------------
--                        EVENTS                          --
------------------------------------------------------------
local dubleXP_store = {}

AddEventHandler("zahya_xplevel:GiveStoreDoubleXP", function(playerid, hours)
   local _source = playerid
   local xPlayer = ESX.GetPlayerFromId(_source)
   local Playeridentifier = xPlayer.identifier
   local seconds = hours*3600
    -- local seconds = 100
   if xPlayer then
    MySQL.Async.fetchAll(
        'SELECT * FROM store_doublexp WHERE identifier = @identifier',
        {
            ['@identifier'] = Playeridentifier,
        }, function(result)
            if not result[1] then
                MySQL.Async.execute('INSERT INTO store_doublexp (identifier, time) VALUES (@identifier, @time)',
                {
                    ['@identifier']    = Playeridentifier,
                    ['@time']        = seconds,
                })
                TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", _source, true, seconds)
                TriggerClientEvent("esx_misc:watermark_promotion", _source, "doubleXP_store", true)
            else
                print(result[1].identifier)
                local oldtime = tonumber(result[1].time)
                TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", _source, false)
                Wait(2000)
                MySQL.Async.fetchAll("UPDATE store_doublexp SET time = @time WHERE identifier = @identifier",
                {
                    ['@identifier'] = Playeridentifier,
                    ['@data'] = seconds + oldtime
                }
                )

                TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", _source, true, seconds + oldtime)

            end

        end)

   end

end)

RegisterNetEvent("zahya_xplevel:checkStoreDoubleXP")
AddEventHandler("zahya_xplevel:checkStoreDoubleXP", function()
    startDoubleXPforPlayer(source)
end)

function startDoubleXPforPlayer(playerid)
    local _source = playerid
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Playeridentifier = xPlayer.identifier

    MySQL.Async.fetchAll(
        'SELECT * FROM store_doublexp WHERE identifier = @identifier',
        {
            ['@identifier'] = Playeridentifier,
        }, function(result)

        if not result[1] then
             return
        else
            dubleXP_store[Playeridentifier] = true
            if dubleXP then return end
            TriggerClientEvent("esx_misc:watermark_promotion", _source, "doubleXP_store", true)
            TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", _source, true, result[1].time)
        end
    
    end)
end

RegisterNetEvent("zahya_xplevel:reduce_StoreDoubleXP")
AddEventHandler("zahya_xplevel:reduce_StoreDoubleXP", function()
    if dubleXP then
        TriggerClientEvent("esx_misc:watermark_promotion", source, "doubleXP_store", false)
        TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", source, false)
    return
    end
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Playeridentifier = xPlayer.identifier
    
    MySQL.Async.fetchAll(
        'SELECT * FROM store_doublexp WHERE identifier = @identifier',
        {
            ['@identifier'] = Playeridentifier,
        }, function(result)
            result[1].time = tonumber(result[1].time)
        if result[1].time >= 60 then
            local oldtime = tonumber(result[1].time)
            MySQL.Async.fetchAll("UPDATE store_doublexp SET time = @time WHERE identifier = @identifier",
            {
                ['@identifier'] = Playeridentifier,
                ['@time'] = oldtime - 60
            }
            )
        elseif result[1].time <= 0 then
           TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", _source, false)
           TriggerClientEvent("esx_misc:watermark_promotion", _source, "doubleXP_store", false)
           dubleXP_store[Playeridentifier] = false
           
           MySQL.Async.execute('DELETE FROM store_doublexp WHERE identifier = @identifier',
                {
                    ['@identifier']    = Playeridentifier,
                })
        end

    end)
end)

RegisterNetEvent("zahya_xplevel:Remove_StoreDoubleXP")
AddEventHandler("zahya_xplevel:Remove_StoreDoubleXP", function()
    print('++++++')
    -- print('finished double for '..source)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local Playeridentifier = xPlayer.identifier
    
           TriggerClientEvent("esx_misc:updatePromotionTimer_duobleXP_store", _source, false)
           TriggerClientEvent("esx_misc:watermark_promotion", _source, "doubleXP_store", false)
           dubleXP_store[Playeridentifier] = false

           MySQL.Async.execute('DELETE FROM store_doublexp WHERE identifier = @identifier',
                {
                    ['@identifier']    = Playeridentifier,
                })
end)

RegisterNetEvent("zahya_xplevel:InitialPlayerServerSide")
AddEventHandler("zahya_xplevel:InitialPlayerServerSide", function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if xPlayer ~= nil then
        MySQL.Async.fetchAll('SELECT * FROM users WHERE identifier = @identifier', {
            ['@identifier'] = xPlayer.identifier
        }, function(result)
            if #result > 0 then

                if result[1]["rp_xp"] == nil or result[1]["rp_rank"] == nil then
                    TriggerClientEvent("esx_xp:print", _source, _("err_db_columns"))
                else
                    local CurrentXP = tonumber(result[1]["rp_xp"])
                    local CurrentRank = tonumber(result[1]["rp_rank"])  

                    xPlayer.set("xp", CurrentXP)
                    xPlayer.set("rank", CurrentRank)       
                    
                    if Config.Leaderboard.Enabled then
                        FetchActivePlayers(_source, CurrentXP, CurrentRank)
                    else
                        TriggerClientEvent("zahya_xplevel:InitialPlayerClientSide", _source, CurrentXP, CurrentRank, false)
                    end
                end
            else
                TriggerClientEvent("esx_xp:print", _source, _("err_db_user"))
            end
        end)
	end
end)

-- Set the current player XP
RegisterNetEvent("zahya_xplevel:setXP")
AddEventHandler("zahya_xplevel:setXP", function(xp, none, pas)
    if(pas==Config.pas)then
    UpdatePlayer(source, xp)
	end
end)

-- Fetch Players Data
RegisterNetEvent("zahya_xplevel:getPlayerData")
AddEventHandler("zahya_xplevel:getPlayerData", function(pas)
    if(pas==Config.pas)then
    local _source = source
    MySQL.Async.fetchAll('SELECT * FROM users', {}, function(players)
        if #players > 0 then     
            TriggerClientEvent("esx_xp:setPlayerData", _source, GetOnlinePlayers(_source, players))
        end
    end) 
	end
end)

function SendLog (name, title, message, color, WebHook)
	local DiscordWebHook = WebHook
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "نظام الفل"}
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end


AddEventHandler("zahya_xplevel:updateCurrentPlayerXP", function(playerId, Action, amount, x, z)
    local xTarget = ESX.GetPlayerFromId(playerId)
    local Reason = x
    local pas = z
	local ids = ExtractIdentifiers(playerId)
	_steamID ="\n**Steam ID:  ** " ..ids.steam..""
	_discordID ="\n**Discord ID:  ** <@" ..ids.discord:gsub("discord:", "")..">"
    -- discord Log
	if not Reason then
	Reason = 'لايوجد سبب محدد'
	end
	if not z then
	z = 'none'
	end
	
	
    if xTarget ~= nil then
	if(Action=='add' or Action=='addnoduble' or Action=='addNoduble' or Action=='noduble' or Action=='Noduble')then
	local duble = ''
    if (dubleXP or dubleXP_store[xTarget.identifier]) and Action=='add' then
        amount = amount * 2
		duble = '\nتم تطبيق ضعف الخبرة '
    end	
        if IsInt(amount) then
            local NewXP = tonumber(xTarget.get("xp")) + amount

            xTarget.set("xp", NewXP)

            UpdatePlayer(playerId)

			SendLog ('زيادة خبرة', '🌐 زيادة خبرة ', 'اللاعب: '..xTarget.getName()..' | '..GetPlayerName(xTarget.source)..' '.._steamID..' '.._discordID..'\n العدد: '..amount..'\nالسبب المرفوق: `'..Reason..'`'..duble, '3447003', 'غيرهاs/1273838365732507658/jmdv59tcc6667rCktTpzJ-i4c5tWwwijiXzT001K5lW145qSO-DiZ12P3-zqu68YZENH')
       end
	elseif Action=='remove' then
	if IsInt(amount) then
            local NewXP = tonumber(xTarget.get("xp")) - amount
            xTarget.set("xp", NewXP)
            UpdatePlayer(playerId)
			SendLog ('نقص خبرة', '🌐 نقص خبرة ', 'اللاعب: '..xTarget.getName()..' | '..GetPlayerName(xTarget.source)..' '.._steamID..' '.._discordID..'\n العدد: '..amount..'\nالسبب المرفوق: `'..Reason..'`', '15158332', 'غيرهاs/1273838365732507658/jmdv59tcc6667rCktTpzJ-i4c5tWwwijiXzT001K5lW145qSO-DiZ12P3-zqu68YZENH')
        end
    end
    end
end)

RegisterServerEvent("zahya_xplevel:updateCurrentPlayerXP_clientSide")
AddEventHandler("zahya_xplevel:updateCurrentPlayerXP_clientSide", function(Action, amount, x, z)
    local playerId = source
    local xTarget = ESX.GetPlayerFromId(playerId)
    local Reason = x
    local pas = z
	local ids = ExtractIdentifiers(playerId)
	_steamID ="\n**Steam ID:  ** " ..ids.steam..""
	_discordID ="\n**Discord ID:  ** <@" ..ids.discord:gsub("discord:", "")..">"
    -- discord Log
	if not Reason then
	Reason = 'لايوجد سبب محدد'
	end
	
	
    if xTarget ~= nil then
	if(Action=='add' or Action=='addnoduble' or Action=='addNoduble' or Action=='noduble' or Action=='Noduble')then
    local duble = ''
    if dubleXP and Action=='add' then
        amount = amount * 2
		duble = '\nتم تطبيق ضعف الخبرة '
    end	
        if IsInt(amount) then
            local NewXP = tonumber(xTarget.get("xp")) + amount
            xTarget.set("xp", NewXP)
            UpdatePlayer(playerId)
			SendLog ('زيادة خبرة', '🌐 زيادة خبرة clientSide', 'اللاعب: '..xTarget.getName()..' | '..GetPlayerName(xTarget.source)..' '.._steamID..' '.._discordID..'\n العدد: '..amount..'\nالسبب المرفوق: `'..Reason..'`'..duble, '3447003', 'غيرهاs/1273838365732507658/jmdv59tcc6667rCktTpzJ-i4c5tWwwijiXzT001K5lW145qSO-DiZ12P3-zqu68YZENH')
       end
	elseif Action=='remove' then
	if IsInt(amount) then
            local NewXP = tonumber(xTarget.get("xp")) - amount
            xTarget.set("xp", NewXP)
            UpdatePlayer(playerId)
			SendLog ('نقص خبرة', '🌐 نقص خبرة clientSide', 'اللاعب: '..xTarget.getName()..' | '..GetPlayerName(xTarget.source)..' '.._steamID..' '.._discordID..'\n العدد: '..amount..'\nالسبب المرفوق: `'..Reason..'`', '15158332', 'غيرهاs/1273838365732507658/jmdv59tcc6667rCktTpzJ-i4c5tWwwijiXzT001K5lW145qSO-DiZ12P3-zqu68YZENH')
        end
    end
    end
end)

function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = "",
        fivem = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
		elseif string.find(id, "fivem") then
            identifiers.fivem = id
        end
    end

    return identifiers
end

--[[
RegisterNetEvent("esx_xp:setRank")
AddEventHandler("esx_xp:setRank", function(playerId, Rank)
    local GoalRank = tonumber(Rank)

    if not GoalRank then
        --
    else
        if Config.Ranks[GoalRank] ~= nil then
            UpdatePlayer(playerId, tonumber(Config.Ranks[GoalRank].XP))
        end
    end
end)]]

RegisterServerEvent("zahya_xplevel:togglePromotion")
AddEventHandler("zahya_xplevel:togglePromotion", function(name, steamIdentifiers, discordIdentifiers, ingamename)
    local xPlayer = ESX.GetPlayerFromId(source)
	local name = GetPlayerName(source)
    local steamIdentifiers = GetPlayerIdentifiers(source)[1]
    local ingamename = ''
    if source ~= 0 then
        ingamename = xPlayer.getName()
    end
	if source == 0 or (xPlayer and (xPlayer.job.name == 'admin' and xPlayer.getGroup() == "superadmin" or xPlayer.getGroup() == "admin" or xPlayer.getGroup() == "aplus" or xPlayer.getGroup() == "a" or xPlayer.getGroup() == "modplus")) then
    if not dubleXP then
	------------------------------------------
    dubleXP = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'doubleXP', true)
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'doubleXP12', true)
	TriggerClientEvent("zahya_xplevel:Promotion_client", -1, true)
	dlxpmainlogsserver((' ضعف الخبرة '),"تفعيل ضعف الخبرة", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n \nInGame name: `"..ingamename.."`",15844367)
	------------------------------------------
	else
	------------------------------------------
	dubleXP = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'doubleXP', false)
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'doubleXP12', false)
	TriggerClientEvent("zahya_xplevel:Promotion_client", -1, false)
	dlxpmainlogsserver((' ضعف الخبرة '),"إنهاء ضعف الخبرة", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n \nInGame name: `"..ingamename.."`",15158332)
	------------------------------------------
    for _, playerId in ipairs(GetPlayers()) do
        startDoubleXPforPlayer(playerId)
        Wait(400)
    end
    ------------------------------------------
  end
  else
  print(('zahya_xplevel: %s attempted to toggle Promotion (not admin!)!'):format(xPlayer.identifier))
  end
end)

RegisterServerEvent('esx_misc:GetCache')
AddEventHandler('esx_misc:GetCache', function()
    local xPlayer = ESX.GetPlayerFromId(source)
	------------------------------------------
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'doubleXP', dubleXP)
	TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'doubleXP12', dubleXP)
	TriggerClientEvent("zahya_xplevel:Promotion_client", source, dubleXP)
	------------------------------------------
end)

local xp = 0 
local done = false 

RegisterServerEvent("zahya_xplevel:SendRank")
AddEventHandler("zahya_xplevel:SendRank", function(xpp)
	xp = xpp
	done = true 
end)

ESX.RegisterServerCallback('zahya_xplevel:getRank', function(source, cb, id)
	done = false
	TriggerClientEvent("zahya_xplevel:getRank_cl", id)
	while not done do Wait(0) end 
	cb(xp)
end)
--[[
#how to use milan_xplevel:getRank Server Callback

ESX.TriggerServerCallback('milan_xplevel:getxp', function(xp)
ESX.ShowNotification('<font color=#5DADE2>'..xp..'</font> خبرة اللاعب')
end, GetPlayerServerId(closestPlayer))
]]


------------------------------------------------------------
--                    ADMIN COMMANDS                      --
------------------------------------------------------------

function DisplayError(playerId, message)
    TriggerClientEvent('chat:addMessage', playerId, {
        color = { 255, 0, 0 },
        args = { "zahya_xplevel", message }
    })    
end
 
--[[
RegisterCommand("esxp_give", function(source, args, rawCommand)
    local playerId = tonumber(args[1])
    local xPlayer = ESX.GetPlayerFromId(playerId)
	
	local xp = tonumber(args[2])
	
	local DiscordWebHook1 = "غيرهاs/900323475879788555/RBdl8Iep92Hc9XNCalBhKHD-3YT1iQw3otARyx61J0iBACLueLzn0kljJlc00aC4mM5R"
	
	local embeds1 = {
		{
			["title"]= "give xp",
			["type"]= "rich",
            ["description"] = "xp : `"..xp.."` \n by the admin `"..GetPlayerName(source).."` \n`"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."` \n الاعب الذي تم إعطائه `"..GetPlayerName(playerId).."` \n`"..GetPlayerIdentifiers(playerId)[1].."` | `"..GetPlayerIdentifiers(playerId)[5].."`",
			["color"] = "10181046",
			["footer"]=  { ["text"]= "/esxp_give by the admin "..GetPlayerName(source)}
		}
	}
    
    if xPlayer == nil then
        return DisplayError(source, _('err_invalid_player'))
    end

    if not xp then
        return DisplayError(source, _('err_invalid_type', "XP", 'integer'))
    end

    UpdatePlayer(playerId, tonumber(xPlayer.get("xp")) + xp)
	
	
	--if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook1, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds1}), { ['Content-Type'] = 'application/json' })
	
end, true)

RegisterCommand("esxp_take", function(source, args, rawCommand)
    local playerId = tonumber(args[1])
    local xPlayer = ESX.GetPlayerFromId(playerId)
	
	local xp = tonumber(args[2])
	
	local DiscordWebHook2 = "غيرهاs/900334593805279242/kToD3gxIN0YFCEcidnYPw1z03lPEZdxUXoH20H8LBz7VSc3fpyZW47ibyJq9PfX911I9"
	
	local embeds2 = {
		{
			["title"]= "remove xp | خصم خبرة",
			["type"]= "rich",
            ["description"] = "xp : `"..xp.."` \n by the admin `"..GetPlayerName(source).."` \n`"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."` \n الاعب الذي تم الخصم منه `"..GetPlayerName(playerId).."` \n`"..GetPlayerIdentifiers(playerId)[1].."` | `"..GetPlayerIdentifiers(playerId)[5].."`",
			["color"] = "10181046",
			["footer"]=  { ["text"]= "/esxp_take by the admin "..GetPlayerName(source)}
		}
	}
    
    if xPlayer == nil then
        return DisplayError(source, _('err_invalid_player'))
    end

    if not xp then
        return DisplayError(source, _('err_invalid_type', "XP", 'integer'))
    end    
    
    UpdatePlayer(playerId, tonumber(xPlayer.get("xp")) - xp)
	
	PerformHttpRequest(DiscordWebHook2, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds2}), { ['Content-Type'] = 'application/json' })
	
end, true) 

RegisterCommand("esxp_set", function(source, args, rawCommand)
    local playerId = tonumber(args[1])
    local xPlayer = ESX.GetPlayerFromId(playerId)
	
	local xp = tonumber(args[2])
	
	local DiscordWebHook3 = "غيرهاs/900337895599136768/RvVYNeL4TgeqMhylsY2lpcMBMs9-7ctst9ASFAKqWot3LvQByc9IeES1wlfnWJjFbpHD"
	
	local embeds3 = {
		{
			["title"]= "set xp | إعادة ضبط خبرة",
			["type"]= "rich",
            ["description"] = "الخبرة التي تم إعادة الضبط لها : `"..xp.."` \n by the admin `"..GetPlayerName(source).."` \n`"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."` \n الاعب `"..GetPlayerName(playerId).."` \n`"..GetPlayerIdentifiers(playerId)[1].."` | `"..GetPlayerIdentifiers(playerId)[5].."`",
			["color"] = "10181046",
			["footer"]=  { ["text"]= "/esxp_set by the admin "..GetPlayerName(source)}
		}
	}
    
    if xPlayer == nil then
        return DisplayError(source, _('err_invalid_player'))
    end

    if not xp then
        return DisplayError(source, _('err_invalid_type', "XP", 'integer'))
    end  

    UpdatePlayer(playerId, xp)
	PerformHttpRequest(DiscordWebHook3, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds3}), { ['Content-Type'] = 'application/json' })
end, true)

RegisterCommand("esxp_rank", function(source, args, rawCommand)
    local playerId = tonumber(args[1])
    local xPlayer = ESX.GetPlayerFromId(playerId)
	
	local goalRank = tonumber(args[2])
	
	local DiscordWebHook4 = "غيرهاs/900339685388353546/mWlZAM5AfRDGzxPEAKu8ciNU4n0FBEknwf-LYBxw1rJ9QZ5PeFNeH2Ui9iuIsjXivEoI"
	
	local embeds4 = {
		{
			["title"]= "set rank | إعادة ضبط لفل",
			["type"]= "rich",
            ["description"] = "الفل الي تم إعادة الضبط له : `"..goalRank.."` \n by the admin `"..GetPlayerName(source).."` \n`"..GetPlayerIdentifiers(source)[1].."` | `"..GetPlayerIdentifiers(source)[5].."` \n الاعب `"..GetPlayerName(playerId).."` \n`"..GetPlayerIdentifiers(playerId)[1].."` | `"..GetPlayerIdentifiers(playerId)[5].."`",
			["color"] = "10181046",
			["footer"]=  { ["text"]= "/esxp_rank by the admin "..GetPlayerName(source)}
		}
	}
    
    if xPlayer == nil then
        return DisplayError(source, _('err_invalid_player'))
    end

    if not goalRank then
        return DisplayError(source, _('err_invalid_type', "Rank", 'integer'))
    end

    if goalRank < 1 or goalRank > #Config.Ranks then
        return DisplayError(source, _('err_invalid_rank', #Config.Ranks))
    end

    local xp = Config.Ranks[goalRank].XP

    UpdatePlayer(playerId, xp)
	PerformHttpRequest(DiscordWebHook4, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds4}), { ['Content-Type'] = 'application/json' })
	
end, true)]]
