2025/06/16-23:09:48.742865 6cac RocksDB version: 8.3.0
2025/06/16-23:09:48.743073 6cac DB SUMMARY
2025/06/16-23:09:48.743090 6cac DB Session ID:  VCKAHCZQB2LPAO6OISAG
2025/06/16-23:09:48.743909 6cac CURRENT file:  CURRENT
2025/06/16-23:09:48.743923 6cac IDENTITY file:  IDENTITY
2025/06/16-23:09:48.743961 6cac MANIFEST file:  MANIFEST-005853 size: 476 Bytes
2025/06/16-23:09:48.743968 6cac SST files in G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default dir, Total Num: 2, files: 005848.sst 005851.sst 
2025/06/16-23:09:48.744022 6cac Write Ahead Log file in G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default: 005852.log size: 764 ; 
2025/06/16-23:09:48.744031 6cac                         Options.error_if_exists: 0
2025/06/16-23:09:48.744038 6cac                       Options.create_if_missing: 1
2025/06/16-23:09:48.744141 6cac                         Options.paranoid_checks: 1
2025/06/16-23:09:48.744151 6cac             Options.flush_verify_memtable_count: 1
2025/06/16-23:09:48.744154 6cac                               Options.track_and_verify_wals_in_manifest: 0
2025/06/16-23:09:48.744156 6cac        Options.verify_sst_unique_id_in_manifest: 1
2025/06/16-23:09:48.744158 6cac                                     Options.env: 000001EE525BC5E0
2025/06/16-23:09:48.744161 6cac                                      Options.fs: WinFS
2025/06/16-23:09:48.744164 6cac                                Options.info_log: 000001EE522E9D70
2025/06/16-23:09:48.744166 6cac                Options.max_file_opening_threads: 16
2025/06/16-23:09:48.744168 6cac                              Options.statistics: 0000000000000000
2025/06/16-23:09:48.744170 6cac                               Options.use_fsync: 0
2025/06/16-23:09:48.744172 6cac                       Options.max_log_file_size: 0
2025/06/16-23:09:48.744175 6cac                  Options.max_manifest_file_size: 1073741824
2025/06/16-23:09:48.744177 6cac                   Options.log_file_time_to_roll: 0
2025/06/16-23:09:48.744179 6cac                       Options.keep_log_file_num: 10
2025/06/16-23:09:48.744181 6cac                    Options.recycle_log_file_num: 0
2025/06/16-23:09:48.744183 6cac                         Options.allow_fallocate: 1
2025/06/16-23:09:48.744185 6cac                        Options.allow_mmap_reads: 0
2025/06/16-23:09:48.744188 6cac                       Options.allow_mmap_writes: 0
2025/06/16-23:09:48.744190 6cac                        Options.use_direct_reads: 0
2025/06/16-23:09:48.744192 6cac                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/16-23:09:48.744194 6cac          Options.create_missing_column_families: 0
2025/06/16-23:09:48.744196 6cac                              Options.db_log_dir: 
2025/06/16-23:09:48.744198 6cac                                 Options.wal_dir: 
2025/06/16-23:09:48.744200 6cac                Options.table_cache_numshardbits: 6
2025/06/16-23:09:48.744202 6cac                         Options.WAL_ttl_seconds: 0
2025/06/16-23:09:48.744205 6cac                       Options.WAL_size_limit_MB: 0
2025/06/16-23:09:48.744207 6cac                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/16-23:09:48.744209 6cac             Options.manifest_preallocation_size: 4194304
2025/06/16-23:09:48.744212 6cac                     Options.is_fd_close_on_exec: 1
2025/06/16-23:09:48.744214 6cac                   Options.advise_random_on_open: 1
2025/06/16-23:09:48.744216 6cac                    Options.db_write_buffer_size: 0
2025/06/16-23:09:48.744218 6cac                    Options.write_buffer_manager: 000001EE525BC6D0
2025/06/16-23:09:48.744220 6cac         Options.access_hint_on_compaction_start: 1
2025/06/16-23:09:48.744222 6cac           Options.random_access_max_buffer_size: 1048576
2025/06/16-23:09:48.744224 6cac                      Options.use_adaptive_mutex: 0
2025/06/16-23:09:48.744226 6cac                            Options.rate_limiter: 0000000000000000
2025/06/16-23:09:48.744229 6cac     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/16-23:09:48.744231 6cac                       Options.wal_recovery_mode: 2
2025/06/16-23:09:48.744254 6cac                  Options.enable_thread_tracking: 0
2025/06/16-23:09:48.744257 6cac                  Options.enable_pipelined_write: 0
2025/06/16-23:09:48.744260 6cac                  Options.unordered_write: 0
2025/06/16-23:09:48.744262 6cac         Options.allow_concurrent_memtable_write: 1
2025/06/16-23:09:48.744264 6cac      Options.enable_write_thread_adaptive_yield: 1
2025/06/16-23:09:48.744266 6cac             Options.write_thread_max_yield_usec: 100
2025/06/16-23:09:48.744268 6cac            Options.write_thread_slow_yield_usec: 3
2025/06/16-23:09:48.744270 6cac                               Options.row_cache: None
2025/06/16-23:09:48.744272 6cac                              Options.wal_filter: None
2025/06/16-23:09:48.744275 6cac             Options.avoid_flush_during_recovery: 0
2025/06/16-23:09:48.744277 6cac             Options.allow_ingest_behind: 0
2025/06/16-23:09:48.744279 6cac             Options.two_write_queues: 0
2025/06/16-23:09:48.744281 6cac             Options.manual_wal_flush: 0
2025/06/16-23:09:48.744283 6cac             Options.wal_compression: 0
2025/06/16-23:09:48.744285 6cac             Options.atomic_flush: 0
2025/06/16-23:09:48.744287 6cac             Options.avoid_unnecessary_blocking_io: 0
2025/06/16-23:09:48.744289 6cac                 Options.persist_stats_to_disk: 0
2025/06/16-23:09:48.744291 6cac                 Options.write_dbid_to_manifest: 0
2025/06/16-23:09:48.744293 6cac                 Options.log_readahead_size: 0
2025/06/16-23:09:48.744295 6cac                 Options.file_checksum_gen_factory: Unknown
2025/06/16-23:09:48.744298 6cac                 Options.best_efforts_recovery: 0
2025/06/16-23:09:48.744300 6cac                Options.max_bgerror_resume_count: 2147483647
2025/06/16-23:09:48.744302 6cac            Options.bgerror_resume_retry_interval: 1000000
2025/06/16-23:09:48.744304 6cac             Options.allow_data_in_errors: 0
2025/06/16-23:09:48.744306 6cac             Options.db_host_id: __hostname__
2025/06/16-23:09:48.744308 6cac             Options.enforce_single_del_contracts: true
2025/06/16-23:09:48.744310 6cac             Options.max_background_jobs: 2
2025/06/16-23:09:48.744313 6cac             Options.max_background_compactions: -1
2025/06/16-23:09:48.744315 6cac             Options.max_subcompactions: 1
2025/06/16-23:09:48.744317 6cac             Options.avoid_flush_during_shutdown: 0
2025/06/16-23:09:48.744319 6cac           Options.writable_file_max_buffer_size: 1048576
2025/06/16-23:09:48.744321 6cac             Options.delayed_write_rate : 16777216
2025/06/16-23:09:48.744323 6cac             Options.max_total_wal_size: 0
2025/06/16-23:09:48.744325 6cac             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/16-23:09:48.744327 6cac                   Options.stats_dump_period_sec: 600
2025/06/16-23:09:48.744330 6cac                 Options.stats_persist_period_sec: 600
2025/06/16-23:09:48.744332 6cac                 Options.stats_history_buffer_size: 1048576
2025/06/16-23:09:48.744334 6cac                          Options.max_open_files: -1
2025/06/16-23:09:48.744336 6cac                          Options.bytes_per_sync: 0
2025/06/16-23:09:48.744338 6cac                      Options.wal_bytes_per_sync: 0
2025/06/16-23:09:48.744341 6cac                   Options.strict_bytes_per_sync: 0
2025/06/16-23:09:48.744343 6cac       Options.compaction_readahead_size: 0
2025/06/16-23:09:48.744345 6cac                  Options.max_background_flushes: -1
2025/06/16-23:09:48.744347 6cac Compression algorithms supported:
2025/06/16-23:09:48.744543 6cac 	kZSTD supported: 0
2025/06/16-23:09:48.744546 6cac 	kSnappyCompression supported: 0
2025/06/16-23:09:48.744548 6cac 	kBZip2Compression supported: 0
2025/06/16-23:09:48.744551 6cac 	kZlibCompression supported: 1
2025/06/16-23:09:48.744553 6cac 	kLZ4Compression supported: 1
2025/06/16-23:09:48.744555 6cac 	kXpressCompression supported: 0
2025/06/16-23:09:48.744557 6cac 	kLZ4HCCompression supported: 1
2025/06/16-23:09:48.744559 6cac 	kZSTDNotFinalCompression supported: 0
2025/06/16-23:09:48.744580 6cac Fast CRC32 supported: Not supported on x86
2025/06/16-23:09:48.744584 6cac DMutex implementation: std::mutex
2025/06/16-23:09:48.745982 6cac [db\version_set.cc:5791] Recovering from manifest file: G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/MANIFEST-005853
2025/06/16-23:09:48.747396 6cac [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/16-23:09:48.747410 6cac               Options.comparator: leveldb.BytewiseComparator
2025/06/16-23:09:48.747412 6cac           Options.merge_operator: None
2025/06/16-23:09:48.747415 6cac        Options.compaction_filter: None
2025/06/16-23:09:48.747417 6cac        Options.compaction_filter_factory: None
2025/06/16-23:09:48.747419 6cac  Options.sst_partitioner_factory: None
2025/06/16-23:09:48.747421 6cac         Options.memtable_factory: SkipListFactory
2025/06/16-23:09:48.747423 6cac            Options.table_factory: BlockBasedTable
2025/06/16-23:09:48.747447 6cac            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EE5624CFC0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001EE525BBE70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/16-23:09:48.747450 6cac        Options.write_buffer_size: 67108864
2025/06/16-23:09:48.747452 6cac  Options.max_write_buffer_number: 2
2025/06/16-23:09:48.747455 6cac          Options.compression: LZ4
2025/06/16-23:09:48.747457 6cac                  Options.bottommost_compression: Disabled
2025/06/16-23:09:48.747459 6cac       Options.prefix_extractor: nullptr
2025/06/16-23:09:48.747461 6cac   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/16-23:09:48.747464 6cac             Options.num_levels: 7
2025/06/16-23:09:48.747466 6cac        Options.min_write_buffer_number_to_merge: 1
2025/06/16-23:09:48.747468 6cac     Options.max_write_buffer_number_to_maintain: 0
2025/06/16-23:09:48.747470 6cac     Options.max_write_buffer_size_to_maintain: 0
2025/06/16-23:09:48.747472 6cac            Options.bottommost_compression_opts.window_bits: -14
2025/06/16-23:09:48.747474 6cac                  Options.bottommost_compression_opts.level: 32767
2025/06/16-23:09:48.747476 6cac               Options.bottommost_compression_opts.strategy: 0
2025/06/16-23:09:48.747478 6cac         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/16-23:09:48.747481 6cac         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/16-23:09:48.747483 6cac         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/16-23:09:48.747485 6cac                  Options.bottommost_compression_opts.enabled: false
2025/06/16-23:09:48.747487 6cac         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/16-23:09:48.747489 6cac         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/16-23:09:48.747492 6cac            Options.compression_opts.window_bits: -14
2025/06/16-23:09:48.747494 6cac                  Options.compression_opts.level: 32767
2025/06/16-23:09:48.747496 6cac               Options.compression_opts.strategy: 0
2025/06/16-23:09:48.747501 6cac         Options.compression_opts.max_dict_bytes: 0
2025/06/16-23:09:48.747504 6cac         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/16-23:09:48.747506 6cac         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/16-23:09:48.747509 6cac         Options.compression_opts.parallel_threads: 1
2025/06/16-23:09:48.747511 6cac                  Options.compression_opts.enabled: false
2025/06/16-23:09:48.747513 6cac         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/16-23:09:48.747545 6cac      Options.level0_file_num_compaction_trigger: 4
2025/06/16-23:09:48.747568 6cac          Options.level0_slowdown_writes_trigger: 20
2025/06/16-23:09:48.747572 6cac              Options.level0_stop_writes_trigger: 36
2025/06/16-23:09:48.747575 6cac                   Options.target_file_size_base: 67108864
2025/06/16-23:09:48.747578 6cac             Options.target_file_size_multiplier: 1
2025/06/16-23:09:48.747581 6cac                Options.max_bytes_for_level_base: 268435456
2025/06/16-23:09:48.747589 6cac Options.level_compaction_dynamic_level_bytes: 0
2025/06/16-23:09:48.747591 6cac          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/16-23:09:48.747596 6cac Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/16-23:09:48.747599 6cac Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/16-23:09:48.747602 6cac Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/16-23:09:48.747604 6cac Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/16-23:09:48.747608 6cac Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/16-23:09:48.747611 6cac Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/16-23:09:48.747614 6cac Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/16-23:09:48.747616 6cac       Options.max_sequential_skip_in_iterations: 8
2025/06/16-23:09:48.747619 6cac                    Options.max_compaction_bytes: 1677721600
2025/06/16-23:09:48.747622 6cac   Options.ignore_max_compaction_bytes_for_input: true
2025/06/16-23:09:48.747625 6cac                        Options.arena_block_size: 1048576
2025/06/16-23:09:48.747627 6cac   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/16-23:09:48.747630 6cac   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/16-23:09:48.747633 6cac                Options.disable_auto_compactions: 0
2025/06/16-23:09:48.747637 6cac                        Options.compaction_style: kCompactionStyleLevel
2025/06/16-23:09:48.747641 6cac                          Options.compaction_pri: kMinOverlappingRatio
2025/06/16-23:09:48.747644 6cac Options.compaction_options_universal.size_ratio: 1
2025/06/16-23:09:48.747647 6cac Options.compaction_options_universal.min_merge_width: 2
2025/06/16-23:09:48.747650 6cac Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/16-23:09:48.747653 6cac Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/16-23:09:48.747657 6cac Options.compaction_options_universal.compression_size_percent: -1
2025/06/16-23:09:48.747661 6cac Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/16-23:09:48.747664 6cac Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/16-23:09:48.747667 6cac Options.compaction_options_fifo.allow_compaction: 0
2025/06/16-23:09:48.747673 6cac                   Options.table_properties_collectors: 
2025/06/16-23:09:48.747676 6cac                   Options.inplace_update_support: 0
2025/06/16-23:09:48.747679 6cac                 Options.inplace_update_num_locks: 10000
2025/06/16-23:09:48.747683 6cac               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/16-23:09:48.747686 6cac               Options.memtable_whole_key_filtering: 0
2025/06/16-23:09:48.747689 6cac   Options.memtable_huge_page_size: 0
2025/06/16-23:09:48.747692 6cac                           Options.bloom_locality: 0
2025/06/16-23:09:48.747695 6cac                    Options.max_successive_merges: 0
2025/06/16-23:09:48.747752 6cac                Options.optimize_filters_for_hits: 0
2025/06/16-23:09:48.747757 6cac                Options.paranoid_file_checks: 0
2025/06/16-23:09:48.747760 6cac                Options.force_consistency_checks: 1
2025/06/16-23:09:48.747763 6cac                Options.report_bg_io_stats: 0
2025/06/16-23:09:48.747766 6cac                               Options.ttl: 2592000
2025/06/16-23:09:48.747769 6cac          Options.periodic_compaction_seconds: 0
2025/06/16-23:09:48.747772 6cac  Options.preclude_last_level_data_seconds: 0
2025/06/16-23:09:48.747775 6cac    Options.preserve_internal_time_seconds: 0
2025/06/16-23:09:48.747778 6cac                       Options.enable_blob_files: false
2025/06/16-23:09:48.747782 6cac                           Options.min_blob_size: 0
2025/06/16-23:09:48.747785 6cac                          Options.blob_file_size: 268435456
2025/06/16-23:09:48.747789 6cac                   Options.blob_compression_type: NoCompression
2025/06/16-23:09:48.747792 6cac          Options.enable_blob_garbage_collection: false
2025/06/16-23:09:48.747795 6cac      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/16-23:09:48.747799 6cac Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/16-23:09:48.747802 6cac          Options.blob_compaction_readahead_size: 0
2025/06/16-23:09:48.747805 6cac                Options.blob_file_starting_level: 0
2025/06/16-23:09:48.747808 6cac Options.experimental_mempurge_threshold: 0.000000
2025/06/16-23:09:48.751062 6cac [db\version_set.cc:5842] Recovered from manifest file:G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/MANIFEST-005853 succeeded,manifest_file_number is 5853, next_file_number is 5855, last_sequence is 572992, log_number is 5845,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 5845
2025/06/16-23:09:48.751077 6cac [db\version_set.cc:5851] Column family [default] (ID 0), log number is 5845
2025/06/16-23:09:48.751681 6cac [db\db_impl\db_impl_open.cc:636] DB ID: 1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc
2025/06/16-23:09:48.753874 6cac EVENT_LOG_v1 {"time_micros": 1750104588753866, "job": 1, "event": "recovery_started", "wal_files": [5852]}
2025/06/16-23:09:48.753891 6cac [db\db_impl\db_impl_open.cc:1131] Recovering log #5852 mode 2
2025/06/16-23:09:48.766181 6cac EVENT_LOG_v1 {"time_micros": 1750104588766141, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 5856, "file_size": 1275, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 572993, "largest_seqno": 573000, "table_properties": {"data_size": 240, "index_size": 76, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 608, "raw_average_key_size": 76, "raw_value_size": 44, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 8, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750104588, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc", "db_session_id": "VCKAHCZQB2LPAO6OISAG", "orig_file_number": 5856, "seqno_to_time_mapping": "N/A"}}
2025/06/16-23:09:48.767061 6cac EVENT_LOG_v1 {"time_micros": 1750104588767055, "job": 1, "event": "recovery_finished"}
2025/06/16-23:09:48.767381 6cac [db\version_set.cc:5304] Creating manifest 5858
2025/06/16-23:09:48.777384 6cac [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005852.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/16-23:09:48.777413 6cac [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default//LOG.old.1749990066399307
2025/06/16-23:09:48.778047 6cac [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 000001EE526C2F60
2025/06/16-23:09:48.778340 794c (Original Log Time 2025/06/16-23:09:48.778275) [db\db_impl\db_impl_compaction_flush.cc:3398] Compaction nothing to do
2025/06/16-23:09:48.779106 6cac DB pointer 000001EE526830C0
2025/06/16-23:09:48.779937 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/16-23:09:48.779948 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.49 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
  L1      1/0   739.79 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0   742.28 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.04 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.04 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001EE525BBE70#32304 capacity: 32.00 MB seed: 622122374 usage: 0.71 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.000222 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.54 KB,0.00163913%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/16-23:19:48.781565 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/16-23:19:48.781599 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.0 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 7 writes, 7 keys, 7 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.49 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
  L1      1/0   739.79 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0   742.28 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001EE525BBE70#32304 capacity: 32.00 MB seed: 622122374 usage: 0.71 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 7.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.54 KB,0.00163913%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/16-23:29:48.785984 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/16-23:29:48.786113 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 1200.0 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.49 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
  L1      1/0   739.79 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0   742.28 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001EE525BBE70#32304 capacity: 32.00 MB seed: 622122374 usage: 0.71 KB table_size: 1024 occupancy: 2 collections: 3 last_copies: 0 last_secs: 0.000207 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.54 KB,0.00163913%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/16-23:39:48.795457 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/16-23:39:48.795496 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 1800.0 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/16-23:49:48.809203 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/16-23:49:48.810669 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 2400.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/16-23:59:48.814252 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/16-23:59:48.814328 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 3000.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-00:09:48.819660 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-00:09:48.819710 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 3600.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-00:19:48.823387 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-00:19:48.823418 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 4200.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-00:29:48.833566 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-00:29:48.833639 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 4800.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-00:39:48.844304 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-00:39:48.845717 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 5400.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-00:49:48.861176 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-00:49:48.861206 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 6000.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.49 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
  L1      1/0   739.79 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      3/0   742.28 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.012       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6000.1 total, 4800.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001EE525BBE70#32304 capacity: 32.00 MB seed: 622122374 usage: 0.71 KB table_size: 1024 occupancy: 2 collections: 11 last_copies: 0 last_secs: 6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.54 KB,0.00163913%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/17-00:59:48.874151 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-00:59:48.875036 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 6600.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-01:09:48.878955 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-01:09:48.879300 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 7200.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-01:19:48.881739 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-01:19:48.881785 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 7800.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/17-01:29:48.888515 9e50 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-01:29:48.888655 9e50 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 8400.1 total, 600.0 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
