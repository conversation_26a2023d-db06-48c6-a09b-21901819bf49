RegisterCommand("jail", function(src, args, raw, jailReason)

	local xPlayer = ESX.GetPlayerFromId(src)

		if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = args[1]
		local jailTime = tonumber(args[2])
		--local jailReason = jailReason
		local jailReason = args[3]..' '..args[4]..' '..args[5]

		if GetPlayerName(jailPlayer) ~= nil then

			if jailTime ~= nil then
				JailPlayer(jailPlayer, jailTime)

				TriggerClientEvent("esx:showNotification", src, " دقيقة" .. jailTime .. " تم سجنه"..GetPlayerName(jailPlayer))
				TriggerEvent("zahya-logs:server:SendLog", "jail", "**لوق سجن لاعب**", "red", "`: اسم الاداري` \n ".. xPlayer.name .."\n \n `: إسم المواطن` \n ".. GetPlayerName(jailPlayer) .."\n \n `: السبب` \n ".. jailReason .."\n \n `: المدة` \n ".. jailTime .." دقيقة \n")
				
				if args[3] ~= nil then
					GetRPName(jailPlayer, function(Firstname, Lastname)
					    TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة والتفتيش " ,  {198, 40, 40} ,  " تمت احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3"..jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")
					end)
				end
			else
				TriggerClientEvent("esx:showNotification", src, "ﺊﻃﺎﺧ ﻦﺠﺴﻟﺍ ﺓﺪﻣ")
			end
		else
			TriggerClientEvent("esx:showNotification", src, "ﺩﻮﺟﻮﻣ ﺮﻴﻏ ﻑﺮﻌﻤﻟﺍ ﺐﺣﺎﺻ")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "ﺕﺎﻴﺣﻼﺼﻟﺍ ﻚﻠﻤﺗﻻ")
	end
end)

RegisterNetEvent("hamada:jail:admin", function(Playerid, jailTime, reason)
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)

		if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = Playerid
		local jailTime = tonumber(jailTime)
		--local jailReason = jailReason
		local jailReason = reason

		if GetPlayerName(jailPlayer) ~= nil then

			if jailTime ~= nil then
				JailPlayer(jailPlayer, jailTime)

				-- TriggerClientEvent("esx:showNotification", src, " دقيقة" .. jailTime .. " تم سجنه"..GetPlayerName(jailPlayer))
				TriggerEvent("zahya-logs:server:SendLog", "jail", "**لوق سجن لاعب**", "red", "`: اسم الاداري` \n ".. xPlayer.name .."\n \n `: إسم المواطن` \n ".. GetPlayerName(jailPlayer) .."\n \n `: السبب` \n ".. jailReason .."\n \n `: المدة` \n ".. jailTime .." دقيقة \n")
				
				if jailReason ~= nil then
					GetRPName(jailPlayer, function(Firstname, Lastname)
					    TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة والتفتيش " ,  {198, 40, 40} ,  " تمت احالة المدعو: ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3"..jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")
					end)
				end
			else
				TriggerClientEvent("esx:showNotification", src, "ﺊﻃﺎﺧ ﻦﺠﺴﻟﺍ ﺓﺪﻣ")
			end
		else
			TriggerClientEvent("esx:showNotification", src, "ﺩﻮﺟﻮﻣ ﺮﻴﻏ ﻑﺮﻌﻤﻟﺍ ﺐﺣﺎﺻ")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "ﺕﺎﻴﺣﻼﺼﻟﺍ ﻚﻠﻤﺗﻻ")
	end
end)

RegisterCommand("removevisa", function(src, args, raw)

	local xPlayer = ESX.GetPlayerFromId(src)

	if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = args[1]
		local jailTime = tonumber(args[2])
		local jailledPlayer = ESX.GetPlayerFromId(jailPlayer)

		if GetPlayerName(jailPlayer) ~= nil then

			if jailTime ~= nil then
				JailPlayer(jailPlayer, jailTime)

				TriggerClientEvent("esx:showNotification", src, " ﺔﻘﻴﻗﺩ" .. jailTime .. " ﻪﻨﺠﺳ ﻢﺗ"..GetPlayerName(jailPlayer))
				--TriggerClientEvent("esx:showpNotifyNotification", jailledPlayer.source, "لقد تم سحب التأشيرة الخاصة بك يرجى تسجيل الخروج والدخول مجددا لإجتياز الإختبار مرة أخرى - سيتم الإفراج عنك بعد حصولك على التأشيرة مجددا")
		for k, v in pairs(Config.Msg["other"]["citizen"]["notification"]) do 
            Config.Options.text = v
            TriggerClientEvent("pNotify:SendNotification", jailledPlayer.source, Config.Options)
            --Citizen.Wait(5000)
        end
				
			else
				TriggerClientEvent("esx:showNotification", src, "اكتب مدة صحيحة بالارقام")
			end
		else
			TriggerClientEvent("esx:showNotification", src, "الاعب غير متصل")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "لاتملك الصلاحيات الكافية")
	end
end)

--Remove Visa
RegisterNetEvent('esx_visa:KickAndRemoveVisa')
AddEventHandler('esx_visa:KickAndRemoveVisa', function(player, reason, jail, aftertime)
    local xPlayer = ESX.GetPlayerFromId(source)
	local xTarget = ESX.GetPlayerFromId(player)
	local xTargetIdentifier = xTarget.identifier
    if xPlayer.job.name == 'admin' then
	local embeds = { -- Log
	{
	    ["title"]= "قرار تعويض اموال غير شرعية للاعب متصل الان",
	    ["type"]="rich",
	    ["description"] = "**معلومات المراقب :\n"..GetPlayerName(source).."** | **"..GetPlayerIdentifiers(source)[1].."** | **"..GetPlayerIdentifiers(source)[5].."**\nهوية المراقب: **"..xPlayer.getName().."**\n\nمعلومات المواطن :**"..GetPlayerName(player).."** | **"..GetPlayerIdentifiers(player)[1].."** | **"..GetPlayerIdentifiers(player)[5].."**\nهوية المواطن: **"..xTarget.getName().."",
	    ["color"] = "3447003",
	    ["footer"]=  { ["text"]= "سحب تأشيرة بسبب :\n"..reason.."",
	    ["icon_url"] = "https://cdn.discordapp.com/attachments/931300530393874482/931302251513909348/7fd04efde345f231.png"},
	}
	}
	TriggerEvent('esx_license:removeLicense', player, 'visa')
	local chat1 = "⭐ الرقابة و التفتيش "
	if jail then
		TriggerClientEvent('chatMessage', -1, chat1, {198, 40, 40}, " سحب تأشيرة وسجن ^3 ".. ESX.GetPlayerFromId(player).name .. "^0 " .. reason)
	else
		TriggerClientEvent('chatMessage', -1, chat1, {198, 40, 40}, " تم سحب تأشيرة ^3 ".. ESX.GetPlayerFromId(player).name .. "^0 " .. reason)
	end
	print(('esx_adminjob: %s attempted to remove visa to Player %s'):format(xPlayer.identifier,xTarget.identifier))
	PerformHttpRequest("", function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
	TriggerClientEvent('hamada:esx_visa:sendnotif', player, jail, aftertime)
	Wait(aftertime*60*1000)
		if ESX.GetPlayerFromIdentifier(xTargetIdentifier) then
			if jail then
				JailPlayer(player, 15)
				Wait(12000)
			end
			Wait(1000)
			TriggerEvent('hamadavisa:removevisa', player)
			-- DropPlayer(player, 'تم سحب تأشيرتك و طردك من المقاطعة')
		else
			if jail then
				MySQL.Async.execute(
				"UPDATE users SET jail = @newJailTime WHERE identifier = @identifier",
					{
						['@identifier'] = xTarget.identifier,
						['@newJailTime'] = 15
					}
				)
			end
		end
	else
	print(('esx_adminjob: %s attempted to remove visa to Player (not adminjob!)!'):format(xPlayer.identifier))
	end
end)

--Remove Visa
RegisterNetEvent('esx_visa:KickAndRemoveVisaPlayer')
AddEventHandler('esx_visa:KickAndRemoveVisaPlayer', function(player, reason)
    local xPlayer = ESX.GetPlayerFromId(source)
	local xTarget = ESX.GetPlayerFromId(player)
	TriggerEvent('esx_license:removeLicense', player, 'visa')
	JailPlayer(player, 30)
	DropPlayer(player, 'تم سحب تأشيرتك و طردك من المنطقة /بسبب قتل رقابي')
	local chat1 = "⭐ الرقابة و التفتيش "
	local chat2 = " تم سحب تأشيرة ^3 ".. ESX.GetPlayerFromId(player).name .. "^0 السبب : ^3 " .. reason
	TriggerClientEvent('chatMessage', -1, chat1, {198, 40, 40}, chat2)
	print(('esx_adminjob: remove visa to Player %s'):format(xTarget.identifier))
end)

RegisterCommand("unjail", function(src, args)

	local xPlayer = ESX.GetPlayerFromId(src)

	if xPlayer["job"]["name"] == "admin" then

		local jailPlayer = args[1]

		if GetPlayerName(jailPlayer) ~= nil then
			UnJail(jailPlayer)
		else
			TriggerClientEvent("esx:showNotification", src, "ﺩﻮﺟﻮﻣ ﺮﻴﻏ ﻑﺮﻌﻤﻟﺍ ﺐﺣﺎﺻ")
		end
	else
		TriggerClientEvent("esx:showNotification", src, "ﺕﺎﻴﺣﻼﺼﻟﺍ ﻚﻠﻤﺗﻻ")
	end
end)


RegisterServerEvent("esx_jail:jailPlayer")
AddEventHandler("esx_jail:jailPlayer", function(targetSrc, jailTime, jailReason, near)
	local src = source
	local targetSrc = tonumber(targetSrc)
	local xPlayer = ESX.GetPlayerFromId(src)
	local xTarget = ESX.GetPlayerFromId(targetSrc)
    local user = xPlayer.getName()
    local rpname = user	
	-- TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'add', 500, 'السجن')
	JailPlayer(targetSrc, jailTime)
	SendToMilitaryJailLog(source, targetSrc, jailReason, jailTime)
	GetRPName(targetSrc, function(Firstname, Lastname)
		xPlayer.addMoney(5000)
		TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'add', 500, 'السجن')
	    if xPlayer["job"]["name"] == "police" then
			if near then
				TriggerClientEvent('chatMessage', -1, " إدارة الشرطة 👮 | " .. rpname .." " ,  { 17, 69, 191 } ,  " تمت احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر. ^3".." تم تسليم مكافأة وخبرة مقابل نقل السجين.")
				TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'add', 1000, 'السجن')
				xPlayer.addAccountMoney('bank', 100000)
				xPlayer.showNotification('لقد قمت بـ سجن المواطن : <span style="color:red;">'..xTarget.name..'</span><br><br>حصلت على مكافأة : <span style="color:green;">$</span>100000<br>وخبرة : <span style="color:#3498db;">1000</span>', xTarget.name, 15000)
			else
				TriggerClientEvent('chatMessage', -1, " إدارة الشرطة 👮 | " .. rpname .." " ,  { 17, 69, 191 } ,  " تمت احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")
			end
        elseif xPlayer["job"]["name"] == "agent" then
			if near then
        		TriggerClientEvent('chatMessage', -1, " حرس الحدود  💂 | " .. rpname .." " ,  { 78, 198, 78 } ,  " تمت احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر. ^3".." تم تسليم مكافأة وخبرة مقابل نقل السجين.")
				TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'add', 1000, 'السجن')
				xPlayer.addAccountMoney('bank', 100000)
				xPlayer.showNotification('لقد قمت بـ سجن المواطن : <span style="color:red;">'..xTarget.name..'</span><br><br>حصلت على مكافأة : <span style="color:green;">$</span>100000<br>وخبرة : <span style="color:#3498db;">1000</span>', xTarget.name, 15000)
			else
				TriggerClientEvent('chatMessage', -1, " حرس الحدود  💂 | " .. rpname .." " ,  { 78, 198, 78 } ,  " تمت احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")		
			end
        else
		TriggerClientEvent('chatMessage', -1, " ⭐ الرقابة والتفتيش " ,  { 198, 40, 40 } ,  " تمت احالة المدعو/ ^3" ..Firstname .. " " .. Lastname .. " ^0الى السجن بعد إدانته بتهمة ^3" .. jailReason.."^0 لمدة ^3"..jailTime.." ^0شهر ^3")
		end
	end)
	if near == false then
		TriggerClientEvent("esx:showNotification", src, GetPlayerName(targetSrc) .. " سجن لمدة " .. jailTime .. " دقيقة!")
	end
end)

function SendToDiscord(source, target, reason)
	local xPlayer = ESX.GetPlayerFromId(target)
	local xTarget = ESX.GetPlayerFromId(source)
	local Killedids = ExtractIdentifiers(target)
  local Killerids = ExtractIdentifiers(source)
  Killedids_steamID =Killedids.steam
  Killedids_discordID ="<@" ..Killedids.discord:gsub("discord:", "")..">"
  Killerids_steamID =Killerids.steam
  Killerids_discordID ="<@" ..Killerids.discord:gsub("discord:", "")..">"
  sendToDiscordMain('سجن لاعب', 'سبب : **'..reason..'**', 'اسم المواطن : **'..xPlayer.name..'**\n معرف المواطن : **'..xPlayer.identifier..'**\n معرف ستيم : **'..Killedids_steamID..'** \n معرف ديسكورد : **'..Killedids_discordID..'** \n --------------- \n اسم الشرطي : **'..xTarget.name..'**\n معرف الشرطي : **'..xTarget.identifier..'** \n شرطي ستيم : **'..Killerids_steamID..'** \n الشرطي ديسكورد : **'..Killerids_discordID..'**', 15548997, '')
end

function sendToDiscordMain (name,title,message,color, webhook)
	local DiscordWebHook = webhook
	-- Modify here your discordWebHook username = name, content = message,embeds = embeds
  
  local embeds = {
	  {
		  ["title"]=title,
		  ["type"]="rich",
		  ["description"] = message,
		  ["color"] =color,
		  ["footer"]=  {
			  ["text"]= os.date("%x %X %p"),
		 },
	  }
  }
  
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function SendToMilitaryJailLog(source, target, reason, jailTime)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    local sourceIdentifiers = ExtractIdentifiers(source)
    local targetIdentifiers = ExtractIdentifiers(target)

    local sourceName = xPlayer.getName()
    local targetName = xTarget.getName()
    local sourceJob = xPlayer.job.name
    local webhook = "https://discord.com/api/webhooks/1376950649928814755/nTO5oVnBbcmIdxjV93AWw_GiS-WxGvcNSwtb1e7Vmcxczco4huuFxBSMd2jQgqc8hQYC" -- حط رابط الويب هوك هنا

    local embed = {
        {
            ["color"] = 15158332, -- أحمر
            ["title"] = "🚨 لوق السجن",
            ["description"] = "قام أحد أعضاء الجهات الأمنية بسجن لاعب.",
            ["fields"] = {
                { name = "👮‍♂️ اسم الشرطي", value = sourceName, inline = true },
                { name = "🆔 أيدي الشرطي", value = tostring(source), inline = true },
                { name = "📛 وظيفة الشرطي", value = sourceJob, inline = true },
                { name = "🎯 اسم اللاعب المسجون", value = targetName, inline = true },
                { name = "🆔 أيدي اللاعب", value = tostring(target), inline = true },
                { name = "📌 مدة السجن", value = tostring(jailTime) .. " شهر", inline = true },
                { name = "📄 سبب السجن", value = reason, inline = false },
                { name = "💬 ديسكورد اللاعب", value = "<@" .. targetIdentifiers.discord:gsub("discord:", "") .. ">", inline = true },
                { name = "💬 ديسكورد الشرطي", value = "<@" .. sourceIdentifiers.discord:gsub("discord:", "") .. ">", inline = true },
            },
            ["footer"] = {
                ["text"] = "نظام السجن - سيرفر نجم ✦",
                ["icon_url"] = "https://cdn.discordapp.com/attachments/1310026369794838638/1310026466960084992/logo.png"
            },
            ["timestamp"] = os.date("!%Y-%m-%dT%H:%M:%SZ")
        }
    }

    PerformHttpRequest(webhook, function(err, text, headers) end, 'POST',
        json.encode({username = "نظام السجن", embeds = embed}),
        { ['Content-Type'] = 'application/json' }
    )
end


function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = "",
        fivem = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
		elseif string.find(id, "fivem") then
            identifiers.fivem = id
        end
    end

    return identifiers
end
RegisterServerEvent("esx_jail:unJailPlayer")
AddEventHandler("esx_jail:unJailPlayer", function(targetIdentifier)
	local src = source
	local xPlayer = ESX.GetPlayerFromIdentifier(targetIdentifier)

	if xPlayer ~= nil then
		UnJail(xPlayer.source)
	else
		MySQL.Async.execute(
			"UPDATE users SET jail = @newJailTime WHERE identifier = @identifier",
			{
				['@identifier'] = targetIdentifier,
				['@newJailTime'] = 0
			}
		)

		local embed = {
			{
				["color"] = 16711680,
				["title"] = "إعفاء من السجن",
				["description"] = string.format("🕵️‍♂️ **السارق**: %s\n🆔 **معرف اللاعب**: `%s`\n🏪 **رقم المتجر**: %d\n💰 **المبلغ المسروق**: `%.1f 💵`\n🏦 **الرصيد قبل السرقة**: `%.1f 💵`\n📉 **الرصيد بعد السرقة**: `%.1f 💵`\n🛑 **نظام السرقة**: %s", 
				  targetIdentifier, 
					xPlayer.source, 
					id, 
					xPlayer, 
					xTarget.name,
					xPlayer.name, 
					timestamp),
				["footer"] = {
					["text"] = "سسس"
				},
			}
		}

		PerformHttpRequest("https://discord.com/api/webhooks/1339371078560251926/eFnQIaV2uFnbZ46igFZCQXH_giDWG29m8D4gNyMS7Kv0KLCGtqyTszsZNcSAaK2yc5BN", function(err, text, headers)
			
		end, "POST", json.encode({username = "جننن", embeds = embed}), {["Content-Type"] = "application/json"})
	

	end

	TriggerClientEvent("esx:showNotification", src, xPlayer.name .. " Unjailed!")
end)

RegisterServerEvent("esx_jail:updateJailTime")
AddEventHandler("esx_jail:updateJailTime", function(newJailTime)
	local src = source

	EditJailTime(src, newJailTime)
end)

RegisterServerEvent("esx_jail:prisonWorkReward")
AddEventHandler("esx_jail:prisonWorkReward", function()
	local src = source

	local xPlayer = ESX.GetPlayerFromId(src)

	xPlayer.addMoney(math.random(13, 21))

	TriggerClientEvent("esx:showNotification", src, "Thanks, here you have som cash for food!")
end)

function JailPlayer(jailPlayer, jailTime)
	TriggerClientEvent("esx_jail:jailPlayer", jailPlayer, jailTime)

	EditJailTime(jailPlayer, jailTime)
end

function UnJail(jailPlayer)
	TriggerClientEvent("esx_jail:unJailPlayer", jailPlayer)

	EditJailTime(jailPlayer, 0)
end

function EditJailTime(source, jailTime)

	local src = source

	local xPlayer = ESX.GetPlayerFromId(src)
	local Identifier = xPlayer.identifier

	MySQL.Async.execute(
       "UPDATE users SET jail = @newJailTime WHERE identifier = @identifier",
        {
			['@identifier'] = Identifier,
			['@newJailTime'] = tonumber(jailTime)
		}
	)
end

function GetRPName(playerId, data)
	local Identifier = ESX.GetPlayerFromId(playerId).identifier

	MySQL.Async.fetchAll("SELECT firstname, lastname FROM users WHERE identifier = @identifier", { ["@identifier"] = Identifier }, function(result)

		data(result[1].firstname, result[1].lastname)

	end)
end

ESX.RegisterServerCallback("esx_jail:retrieveJailedPlayers", function(source, cb)
	
	local jailedPersons = {}

	MySQL.Async.fetchAll("SELECT firstname, lastname, jail, identifier FROM users WHERE jail > @jail", { ["@jail"] = 0 }, function(result)

		for i = 1, #result, 1 do
			table.insert(jailedPersons, { name = result[i].firstname .. " " .. result[i].lastname, jailTime = result[i].jail, identifier = result[i].identifier })
		end

		cb(jailedPersons)
	end)
end)

ESX.RegisterServerCallback("esx_jail:retrieveJailTime", function(source, cb)

	local src = source

	local xPlayer = ESX.GetPlayerFromId(src)
	local Identifier = xPlayer.identifier


	MySQL.Async.fetchAll("SELECT jail FROM users WHERE identifier = @identifier", { ["@identifier"] = Identifier }, function(result)

		local JailTime = tonumber(result[1].jail)

		if JailTime > 0 then

			cb(true, JailTime)
		else
			cb(false, 0)
		end

	end)
end)