2025/06/17-01:57:36.663491 7718 RocksDB version: 8.3.0
2025/06/17-01:57:36.663890 7718 DB SUMMARY
2025/06/17-01:57:36.663927 7718 DB Session ID:  5JFGGYL3AZLBL7I18L6U
2025/06/17-01:57:36.665927 7718 CURRENT file:  CURRENT
2025/06/17-01:57:36.666065 7718 IDENTITY file:  IDENTITY
2025/06/17-01:57:36.666210 7718 MANIFEST file:  MANIFEST-005863 size: 852 Bytes
2025/06/17-01:57:36.666229 7718 SST files in G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default dir, Total Num: 4, files: 005848.sst 005851.sst 005856.sst 005861.sst 
2025/06/17-01:57:36.666240 7718 Write Ahead Log file in G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default: 005862.log size: 764 ; 
2025/06/17-01:57:36.666257 7718                         Options.error_if_exists: 0
2025/06/17-01:57:36.666428 7718                       Options.create_if_missing: 1
2025/06/17-01:57:36.666443 7718                         Options.paranoid_checks: 1
2025/06/17-01:57:36.666447 7718             Options.flush_verify_memtable_count: 1
2025/06/17-01:57:36.666450 7718                               Options.track_and_verify_wals_in_manifest: 0
2025/06/17-01:57:36.666454 7718        Options.verify_sst_unique_id_in_manifest: 1
2025/06/17-01:57:36.666457 7718                                     Options.env: 00000276B3268C60
2025/06/17-01:57:36.666462 7718                                      Options.fs: WinFS
2025/06/17-01:57:36.666465 7718                                Options.info_log: 00000276B478A1A0
2025/06/17-01:57:36.666469 7718                Options.max_file_opening_threads: 16
2025/06/17-01:57:36.666472 7718                              Options.statistics: 0000000000000000
2025/06/17-01:57:36.666476 7718                               Options.use_fsync: 0
2025/06/17-01:57:36.666479 7718                       Options.max_log_file_size: 0
2025/06/17-01:57:36.666483 7718                  Options.max_manifest_file_size: 1073741824
2025/06/17-01:57:36.666486 7718                   Options.log_file_time_to_roll: 0
2025/06/17-01:57:36.666490 7718                       Options.keep_log_file_num: 10
2025/06/17-01:57:36.666493 7718                    Options.recycle_log_file_num: 0
2025/06/17-01:57:36.666497 7718                         Options.allow_fallocate: 1
2025/06/17-01:57:36.666500 7718                        Options.allow_mmap_reads: 0
2025/06/17-01:57:36.666504 7718                       Options.allow_mmap_writes: 0
2025/06/17-01:57:36.666507 7718                        Options.use_direct_reads: 0
2025/06/17-01:57:36.666510 7718                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/17-01:57:36.666514 7718          Options.create_missing_column_families: 0
2025/06/17-01:57:36.666517 7718                              Options.db_log_dir: 
2025/06/17-01:57:36.666521 7718                                 Options.wal_dir: 
2025/06/17-01:57:36.666524 7718                Options.table_cache_numshardbits: 6
2025/06/17-01:57:36.666527 7718                         Options.WAL_ttl_seconds: 0
2025/06/17-01:57:36.666531 7718                       Options.WAL_size_limit_MB: 0
2025/06/17-01:57:36.666534 7718                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/17-01:57:36.666538 7718             Options.manifest_preallocation_size: 4194304
2025/06/17-01:57:36.666542 7718                     Options.is_fd_close_on_exec: 1
2025/06/17-01:57:36.666545 7718                   Options.advise_random_on_open: 1
2025/06/17-01:57:36.666549 7718                    Options.db_write_buffer_size: 0
2025/06/17-01:57:36.666552 7718                    Options.write_buffer_manager: 00000276B32687B0
2025/06/17-01:57:36.666555 7718         Options.access_hint_on_compaction_start: 1
2025/06/17-01:57:36.666559 7718           Options.random_access_max_buffer_size: 1048576
2025/06/17-01:57:36.666562 7718                      Options.use_adaptive_mutex: 0
2025/06/17-01:57:36.666566 7718                            Options.rate_limiter: 0000000000000000
2025/06/17-01:57:36.666570 7718     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/17-01:57:36.666574 7718                       Options.wal_recovery_mode: 2
2025/06/17-01:57:36.666613 7718                  Options.enable_thread_tracking: 0
2025/06/17-01:57:36.666619 7718                  Options.enable_pipelined_write: 0
2025/06/17-01:57:36.666623 7718                  Options.unordered_write: 0
2025/06/17-01:57:36.666626 7718         Options.allow_concurrent_memtable_write: 1
2025/06/17-01:57:36.666629 7718      Options.enable_write_thread_adaptive_yield: 1
2025/06/17-01:57:36.666633 7718             Options.write_thread_max_yield_usec: 100
2025/06/17-01:57:36.666636 7718            Options.write_thread_slow_yield_usec: 3
2025/06/17-01:57:36.666640 7718                               Options.row_cache: None
2025/06/17-01:57:36.666643 7718                              Options.wal_filter: None
2025/06/17-01:57:36.666647 7718             Options.avoid_flush_during_recovery: 0
2025/06/17-01:57:36.666650 7718             Options.allow_ingest_behind: 0
2025/06/17-01:57:36.666654 7718             Options.two_write_queues: 0
2025/06/17-01:57:36.666657 7718             Options.manual_wal_flush: 0
2025/06/17-01:57:36.666661 7718             Options.wal_compression: 0
2025/06/17-01:57:36.666664 7718             Options.atomic_flush: 0
2025/06/17-01:57:36.666668 7718             Options.avoid_unnecessary_blocking_io: 0
2025/06/17-01:57:36.666671 7718                 Options.persist_stats_to_disk: 0
2025/06/17-01:57:36.666674 7718                 Options.write_dbid_to_manifest: 0
2025/06/17-01:57:36.666678 7718                 Options.log_readahead_size: 0
2025/06/17-01:57:36.666681 7718                 Options.file_checksum_gen_factory: Unknown
2025/06/17-01:57:36.666685 7718                 Options.best_efforts_recovery: 0
2025/06/17-01:57:36.666688 7718                Options.max_bgerror_resume_count: 2147483647
2025/06/17-01:57:36.666691 7718            Options.bgerror_resume_retry_interval: 1000000
2025/06/17-01:57:36.666695 7718             Options.allow_data_in_errors: 0
2025/06/17-01:57:36.666698 7718             Options.db_host_id: __hostname__
2025/06/17-01:57:36.666702 7718             Options.enforce_single_del_contracts: true
2025/06/17-01:57:36.666705 7718             Options.max_background_jobs: 2
2025/06/17-01:57:36.666709 7718             Options.max_background_compactions: -1
2025/06/17-01:57:36.666712 7718             Options.max_subcompactions: 1
2025/06/17-01:57:36.666716 7718             Options.avoid_flush_during_shutdown: 0
2025/06/17-01:57:36.666719 7718           Options.writable_file_max_buffer_size: 1048576
2025/06/17-01:57:36.666723 7718             Options.delayed_write_rate : 16777216
2025/06/17-01:57:36.666726 7718             Options.max_total_wal_size: 0
2025/06/17-01:57:36.666729 7718             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/17-01:57:36.666733 7718                   Options.stats_dump_period_sec: 600
2025/06/17-01:57:36.666736 7718                 Options.stats_persist_period_sec: 600
2025/06/17-01:57:36.666740 7718                 Options.stats_history_buffer_size: 1048576
2025/06/17-01:57:36.666743 7718                          Options.max_open_files: -1
2025/06/17-01:57:36.666747 7718                          Options.bytes_per_sync: 0
2025/06/17-01:57:36.666750 7718                      Options.wal_bytes_per_sync: 0
2025/06/17-01:57:36.666754 7718                   Options.strict_bytes_per_sync: 0
2025/06/17-01:57:36.666757 7718       Options.compaction_readahead_size: 0
2025/06/17-01:57:36.666760 7718                  Options.max_background_flushes: -1
2025/06/17-01:57:36.666764 7718 Compression algorithms supported:
2025/06/17-01:57:36.666772 7718 	kZSTD supported: 0
2025/06/17-01:57:36.666776 7718 	kSnappyCompression supported: 0
2025/06/17-01:57:36.666780 7718 	kBZip2Compression supported: 0
2025/06/17-01:57:36.666783 7718 	kZlibCompression supported: 1
2025/06/17-01:57:36.666786 7718 	kLZ4Compression supported: 1
2025/06/17-01:57:36.666790 7718 	kXpressCompression supported: 0
2025/06/17-01:57:36.666793 7718 	kLZ4HCCompression supported: 1
2025/06/17-01:57:36.666797 7718 	kZSTDNotFinalCompression supported: 0
2025/06/17-01:57:36.667224 7718 Fast CRC32 supported: Not supported on x86
2025/06/17-01:57:36.667244 7718 DMutex implementation: std::mutex
2025/06/17-01:57:36.670478 7718 [db\version_set.cc:5791] Recovering from manifest file: G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/MANIFEST-005863
2025/06/17-01:57:36.674494 7718 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/17-01:57:36.674525 7718               Options.comparator: leveldb.BytewiseComparator
2025/06/17-01:57:36.674530 7718           Options.merge_operator: None
2025/06/17-01:57:36.674533 7718        Options.compaction_filter: None
2025/06/17-01:57:36.674536 7718        Options.compaction_filter_factory: None
2025/06/17-01:57:36.674540 7718  Options.sst_partitioner_factory: None
2025/06/17-01:57:36.674543 7718         Options.memtable_factory: SkipListFactory
2025/06/17-01:57:36.674546 7718            Options.table_factory: BlockBasedTable
2025/06/17-01:57:36.674581 7718            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000276B46BE5D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000276B32677D0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/17-01:57:36.674586 7718        Options.write_buffer_size: 67108864
2025/06/17-01:57:36.674590 7718  Options.max_write_buffer_number: 2
2025/06/17-01:57:36.674594 7718          Options.compression: LZ4
2025/06/17-01:57:36.674597 7718                  Options.bottommost_compression: Disabled
2025/06/17-01:57:36.674601 7718       Options.prefix_extractor: nullptr
2025/06/17-01:57:36.674604 7718   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-01:57:36.674608 7718             Options.num_levels: 7
2025/06/17-01:57:36.674611 7718        Options.min_write_buffer_number_to_merge: 1
2025/06/17-01:57:36.674615 7718     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-01:57:36.674619 7718     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-01:57:36.674622 7718            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-01:57:36.674626 7718                  Options.bottommost_compression_opts.level: 32767
2025/06/17-01:57:36.674629 7718               Options.bottommost_compression_opts.strategy: 0
2025/06/17-01:57:36.674633 7718         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-01:57:36.674636 7718         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-01:57:36.674641 7718         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-01:57:36.674644 7718                  Options.bottommost_compression_opts.enabled: false
2025/06/17-01:57:36.674647 7718         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-01:57:36.674650 7718         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-01:57:36.674654 7718            Options.compression_opts.window_bits: -14
2025/06/17-01:57:36.674658 7718                  Options.compression_opts.level: 32767
2025/06/17-01:57:36.674662 7718               Options.compression_opts.strategy: 0
2025/06/17-01:57:36.674672 7718         Options.compression_opts.max_dict_bytes: 0
2025/06/17-01:57:36.674678 7718         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-01:57:36.674681 7718         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-01:57:36.674685 7718         Options.compression_opts.parallel_threads: 1
2025/06/17-01:57:36.674690 7718                  Options.compression_opts.enabled: false
2025/06/17-01:57:36.674693 7718         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-01:57:36.674697 7718      Options.level0_file_num_compaction_trigger: 4
2025/06/17-01:57:36.674701 7718          Options.level0_slowdown_writes_trigger: 20
2025/06/17-01:57:36.674704 7718              Options.level0_stop_writes_trigger: 36
2025/06/17-01:57:36.674707 7718                   Options.target_file_size_base: 67108864
2025/06/17-01:57:36.674711 7718             Options.target_file_size_multiplier: 1
2025/06/17-01:57:36.674714 7718                Options.max_bytes_for_level_base: 268435456
2025/06/17-01:57:36.674720 7718 Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-01:57:36.674724 7718          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-01:57:36.674728 7718 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-01:57:36.674732 7718 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-01:57:36.674736 7718 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-01:57:36.674739 7718 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-01:57:36.674743 7718 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-01:57:36.674748 7718 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-01:57:36.674752 7718 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-01:57:36.674755 7718       Options.max_sequential_skip_in_iterations: 8
2025/06/17-01:57:36.674758 7718                    Options.max_compaction_bytes: 1677721600
2025/06/17-01:57:36.674762 7718   Options.ignore_max_compaction_bytes_for_input: true
2025/06/17-01:57:36.674765 7718                        Options.arena_block_size: 1048576
2025/06/17-01:57:36.674769 7718   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-01:57:36.674772 7718   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-01:57:36.674775 7718                Options.disable_auto_compactions: 0
2025/06/17-01:57:36.674780 7718                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-01:57:36.674784 7718                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-01:57:36.674787 7718 Options.compaction_options_universal.size_ratio: 1
2025/06/17-01:57:36.674793 7718 Options.compaction_options_universal.min_merge_width: 2
2025/06/17-01:57:36.674797 7718 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-01:57:36.674800 7718 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-01:57:36.674804 7718 Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-01:57:36.674809 7718 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-01:57:36.674812 7718 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-01:57:36.674815 7718 Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-01:57:36.674824 7718                   Options.table_properties_collectors: 
2025/06/17-01:57:36.674827 7718                   Options.inplace_update_support: 0
2025/06/17-01:57:36.674831 7718                 Options.inplace_update_num_locks: 10000
2025/06/17-01:57:36.674834 7718               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-01:57:36.674838 7718               Options.memtable_whole_key_filtering: 0
2025/06/17-01:57:36.674842 7718   Options.memtable_huge_page_size: 0
2025/06/17-01:57:36.674845 7718                           Options.bloom_locality: 0
2025/06/17-01:57:36.674849 7718                    Options.max_successive_merges: 0
2025/06/17-01:57:36.674919 7718                Options.optimize_filters_for_hits: 0
2025/06/17-01:57:36.674925 7718                Options.paranoid_file_checks: 0
2025/06/17-01:57:36.674928 7718                Options.force_consistency_checks: 1
2025/06/17-01:57:36.674932 7718                Options.report_bg_io_stats: 0
2025/06/17-01:57:36.674935 7718                               Options.ttl: 2592000
2025/06/17-01:57:36.674939 7718          Options.periodic_compaction_seconds: 0
2025/06/17-01:57:36.674942 7718  Options.preclude_last_level_data_seconds: 0
2025/06/17-01:57:36.674946 7718    Options.preserve_internal_time_seconds: 0
2025/06/17-01:57:36.674949 7718                       Options.enable_blob_files: false
2025/06/17-01:57:36.674953 7718                           Options.min_blob_size: 0
2025/06/17-01:57:36.674956 7718                          Options.blob_file_size: 268435456
2025/06/17-01:57:36.674960 7718                   Options.blob_compression_type: NoCompression
2025/06/17-01:57:36.674964 7718          Options.enable_blob_garbage_collection: false
2025/06/17-01:57:36.674967 7718      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-01:57:36.674972 7718 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-01:57:36.674986 7718          Options.blob_compaction_readahead_size: 0
2025/06/17-01:57:36.674990 7718                Options.blob_file_starting_level: 0
2025/06/17-01:57:36.674994 7718 Options.experimental_mempurge_threshold: 0.000000
2025/06/17-01:57:36.687785 7718 [db\version_set.cc:5842] Recovered from manifest file:G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/MANIFEST-005863 succeeded,manifest_file_number is 5863, next_file_number is 5865, last_sequence is 573008, log_number is 5858,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 5858
2025/06/17-01:57:36.687815 7718 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 5858
2025/06/17-01:57:36.688580 7718 [db\db_impl\db_impl_open.cc:636] DB ID: 1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc
2025/06/17-01:57:36.695811 7718 EVENT_LOG_v1 {"time_micros": 1750114656695801, "job": 1, "event": "recovery_started", "wal_files": [5862]}
2025/06/17-01:57:36.695836 7718 [db\db_impl\db_impl_open.cc:1131] Recovering log #5862 mode 2
2025/06/17-01:57:36.714885 7718 EVENT_LOG_v1 {"time_micros": 1750114656714838, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 5866, "file_size": 1275, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 573009, "largest_seqno": 573016, "table_properties": {"data_size": 240, "index_size": 76, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 608, "raw_average_key_size": 76, "raw_value_size": 44, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 8, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750114656, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc", "db_session_id": "5JFGGYL3AZLBL7I18L6U", "orig_file_number": 5866, "seqno_to_time_mapping": "N/A"}}
2025/06/17-01:57:36.716323 7718 EVENT_LOG_v1 {"time_micros": 1750114656716313, "job": 1, "event": "recovery_finished"}
2025/06/17-01:57:36.717023 7718 [db\version_set.cc:5304] Creating manifest 5868
2025/06/17-01:57:36.754820 7718 [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005862.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:57:36.754855 7718 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default//LOG.old.1750072743366315
2025/06/17-01:57:36.755939 7718 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 00000276BC86D810
2025/06/17-01:57:36.757000 5a04 [db\compaction\compaction_job.cc:1992] [default] [JOB 3] Compacting 4@0 + 1@1 files to L1, score 1.00
2025/06/17-01:57:36.757048 5a04 [db\compaction\compaction_job.cc:1996] [default]: Compaction start summary: Base version 2 Base level 0, inputs: [5866(1275B) 5861(1275B) 5856(1275B) 5851(1275B)], [5848(739KB)]
2025/06/17-01:57:36.757094 5a04 EVENT_LOG_v1 {"time_micros": 1750114656757063, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [5866, 5861, 5856, 5851], "files_L1": [5848], "score": 1, "input_data_size": 762646, "oldest_snapshot_seqno": -1}
2025/06/17-01:57:36.758488 7718 DB pointer 00000276C12822C0
2025/06/17-01:57:36.767972 185c [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-01:57:36.767992 185c [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.01 MB/s
Cumulative WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8 writes, 8 keys, 8 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.01 MB/s
Interval WAL: 8 writes, 8 syncs, 1.00 writes per sync, written: 0.00 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    4.98 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.018       0      0       0.0       0.0
  L1      1/1   739.79 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      5/5   744.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.018       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.018       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.02              0.00         1    0.018       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000276B32677D0#45860 capacity: 32.00 MB seed: 622122374 usage: 7.31 KB table_size: 1024 occupancy: 7 collections: 1 last_copies: 0 last_secs: 7.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.54 KB,0.00163913%) Misc(6,6.19 KB,0.0188857%)

** File Read Latency Histogram By Level [default] **
2025/06/17-01:57:36.854944 5a04 [db\compaction\compaction_job.cc:1595] [default] [JOB 3] Generated table #5871: 27519 keys, 757546 bytes, temperature: kUnknown
2025/06/17-01:57:36.855058 5a04 EVENT_LOG_v1 {"time_micros": 1750114656855006, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 5871, "file_size": 757546, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 751065, "index_size": 15909, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1971417, "raw_average_key_size": 71, "raw_value_size": 220513, "raw_average_value_size": 8, "num_data_blocks": 312, "num_entries": 27519, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1725565245, "oldest_key_time": 0, "file_creation_time": 1750114656, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc", "db_session_id": "5JFGGYL3AZLBL7I18L6U", "orig_file_number": 5871, "seqno_to_time_mapping": "N/A"}}
2025/06/17-01:57:36.889889 5a04 (Original Log Time 2025/06/17-01:57:36.889025) [db\compaction\compaction_job.cc:1667] [default] [JOB 3] Compacted 4@0 + 1@1 files to L1 => 757546 bytes
2025/06/17-01:57:36.889910 5a04 (Original Log Time 2025/06/17-01:57:36.889793) [db\compaction\compaction_job.cc:888] [default] compacted to: files[0 1 0 0 0 0 0] max score 0.00, MB/sec: 7.8 rd, 7.7 wr, level 1, files in(4, 1) out(1 +0 blob) MB in(0.0, 0.7 +0.0 blob) out(0.7 +0.0 blob), read-write-amplify(298.1) write-amplify(148.5) OK, records in: 27551, records dropped: 32 output_compression: LZ4
2025/06/17-01:57:36.889918 5a04 (Original Log Time 2025/06/17-01:57:36.889841) EVENT_LOG_v1 {"time_micros": 1750114656889810, "job": 3, "event": "compaction_finished", "compaction_time_micros": 98158, "compaction_time_cpu_micros": 0, "output_level": 1, "num_output_files": 1, "total_output_size": 757546, "num_input_records": 27551, "num_output_records": 27519, "num_subcompactions": 1, "output_compression": "LZ4", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0, 0, 0]}
2025/06/17-01:57:36.890336 5a04 [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005866.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:57:36.890367 5a04 EVENT_LOG_v1 {"time_micros": 1750114656890360, "job": 3, "event": "table_file_deletion", "file_number": 5866}
2025/06/17-01:57:36.890932 5a04 [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005861.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:57:36.890971 5a04 EVENT_LOG_v1 {"time_micros": 1750114656890963, "job": 3, "event": "table_file_deletion", "file_number": 5861}
2025/06/17-01:57:36.891971 5a04 [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005856.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:57:36.892011 5a04 EVENT_LOG_v1 {"time_micros": 1750114656892001, "job": 3, "event": "table_file_deletion", "file_number": 5856}
2025/06/17-01:57:36.893691 5a04 [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005851.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:57:36.893739 5a04 EVENT_LOG_v1 {"time_micros": 1750114656893728, "job": 3, "event": "table_file_deletion", "file_number": 5851}
2025/06/17-01:57:36.894545 5a04 [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005848.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:57:36.894591 5a04 EVENT_LOG_v1 {"time_micros": 1750114656894581, "job": 3, "event": "table_file_deletion", "file_number": 5848}
2025/06/17-01:57:36.894628 5a04 (Original Log Time 2025/06/17-01:57:36.894623) [db\db_impl\db_impl_compaction_flush.cc:3398] Compaction nothing to do
