2025/06/17-01:33:32.469365 734c RocksDB version: 8.3.0
2025/06/17-01:33:32.469641 734c DB SUMMARY
2025/06/17-01:33:32.469678 734c DB Session ID:  MEFMS7RH377QPMTVS7V0
2025/06/17-01:33:32.470551 734c CURRENT file:  CURRENT
2025/06/17-01:33:32.470567 734c IDENTITY file:  IDENTITY
2025/06/17-01:33:32.470606 734c MANIFEST file:  MANIFEST-005858 size: 664 Bytes
2025/06/17-01:33:32.470613 734c SST files in G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default dir, Total Num: 3, files: 005848.sst 005851.sst 005856.sst 
2025/06/17-01:33:32.470620 734c Write Ahead Log file in G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default: 005857.log size: 764 ; 
2025/06/17-01:33:32.470628 734c                         Options.error_if_exists: 0
2025/06/17-01:33:32.470635 734c                       Options.create_if_missing: 1
2025/06/17-01:33:32.470734 734c                         Options.paranoid_checks: 1
2025/06/17-01:33:32.470740 734c             Options.flush_verify_memtable_count: 1
2025/06/17-01:33:32.470742 734c                               Options.track_and_verify_wals_in_manifest: 0
2025/06/17-01:33:32.470745 734c        Options.verify_sst_unique_id_in_manifest: 1
2025/06/17-01:33:32.470747 734c                                     Options.env: 000001F509F10150
2025/06/17-01:33:32.470750 734c                                      Options.fs: WinFS
2025/06/17-01:33:32.470752 734c                                Options.info_log: 000001F50E4407D0
2025/06/17-01:33:32.470754 734c                Options.max_file_opening_threads: 16
2025/06/17-01:33:32.470756 734c                              Options.statistics: 0000000000000000
2025/06/17-01:33:32.470758 734c                               Options.use_fsync: 0
2025/06/17-01:33:32.470761 734c                       Options.max_log_file_size: 0
2025/06/17-01:33:32.470763 734c                  Options.max_manifest_file_size: 1073741824
2025/06/17-01:33:32.470765 734c                   Options.log_file_time_to_roll: 0
2025/06/17-01:33:32.470767 734c                       Options.keep_log_file_num: 10
2025/06/17-01:33:32.470769 734c                    Options.recycle_log_file_num: 0
2025/06/17-01:33:32.470772 734c                         Options.allow_fallocate: 1
2025/06/17-01:33:32.470774 734c                        Options.allow_mmap_reads: 0
2025/06/17-01:33:32.470776 734c                       Options.allow_mmap_writes: 0
2025/06/17-01:33:32.470778 734c                        Options.use_direct_reads: 0
2025/06/17-01:33:32.470780 734c                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/17-01:33:32.470783 734c          Options.create_missing_column_families: 0
2025/06/17-01:33:32.470785 734c                              Options.db_log_dir: 
2025/06/17-01:33:32.470787 734c                                 Options.wal_dir: 
2025/06/17-01:33:32.470789 734c                Options.table_cache_numshardbits: 6
2025/06/17-01:33:32.470791 734c                         Options.WAL_ttl_seconds: 0
2025/06/17-01:33:32.470793 734c                       Options.WAL_size_limit_MB: 0
2025/06/17-01:33:32.470796 734c                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/17-01:33:32.470798 734c             Options.manifest_preallocation_size: 4194304
2025/06/17-01:33:32.470800 734c                     Options.is_fd_close_on_exec: 1
2025/06/17-01:33:32.470802 734c                   Options.advise_random_on_open: 1
2025/06/17-01:33:32.470805 734c                    Options.db_write_buffer_size: 0
2025/06/17-01:33:32.470807 734c                    Options.write_buffer_manager: 000001F509F0EDA0
2025/06/17-01:33:32.470809 734c         Options.access_hint_on_compaction_start: 1
2025/06/17-01:33:32.470811 734c           Options.random_access_max_buffer_size: 1048576
2025/06/17-01:33:32.470813 734c                      Options.use_adaptive_mutex: 0
2025/06/17-01:33:32.470815 734c                            Options.rate_limiter: 0000000000000000
2025/06/17-01:33:32.470818 734c     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/17-01:33:32.470820 734c                       Options.wal_recovery_mode: 2
2025/06/17-01:33:32.470842 734c                  Options.enable_thread_tracking: 0
2025/06/17-01:33:32.470846 734c                  Options.enable_pipelined_write: 0
2025/06/17-01:33:32.470848 734c                  Options.unordered_write: 0
2025/06/17-01:33:32.470850 734c         Options.allow_concurrent_memtable_write: 1
2025/06/17-01:33:32.470852 734c      Options.enable_write_thread_adaptive_yield: 1
2025/06/17-01:33:32.470855 734c             Options.write_thread_max_yield_usec: 100
2025/06/17-01:33:32.470857 734c            Options.write_thread_slow_yield_usec: 3
2025/06/17-01:33:32.470859 734c                               Options.row_cache: None
2025/06/17-01:33:32.470861 734c                              Options.wal_filter: None
2025/06/17-01:33:32.470863 734c             Options.avoid_flush_during_recovery: 0
2025/06/17-01:33:32.470865 734c             Options.allow_ingest_behind: 0
2025/06/17-01:33:32.470868 734c             Options.two_write_queues: 0
2025/06/17-01:33:32.470870 734c             Options.manual_wal_flush: 0
2025/06/17-01:33:32.470872 734c             Options.wal_compression: 0
2025/06/17-01:33:32.470874 734c             Options.atomic_flush: 0
2025/06/17-01:33:32.470876 734c             Options.avoid_unnecessary_blocking_io: 0
2025/06/17-01:33:32.470878 734c                 Options.persist_stats_to_disk: 0
2025/06/17-01:33:32.470880 734c                 Options.write_dbid_to_manifest: 0
2025/06/17-01:33:32.470883 734c                 Options.log_readahead_size: 0
2025/06/17-01:33:32.470885 734c                 Options.file_checksum_gen_factory: Unknown
2025/06/17-01:33:32.470887 734c                 Options.best_efforts_recovery: 0
2025/06/17-01:33:32.470889 734c                Options.max_bgerror_resume_count: 2147483647
2025/06/17-01:33:32.470891 734c            Options.bgerror_resume_retry_interval: 1000000
2025/06/17-01:33:32.470893 734c             Options.allow_data_in_errors: 0
2025/06/17-01:33:32.470895 734c             Options.db_host_id: __hostname__
2025/06/17-01:33:32.470898 734c             Options.enforce_single_del_contracts: true
2025/06/17-01:33:32.470900 734c             Options.max_background_jobs: 2
2025/06/17-01:33:32.470902 734c             Options.max_background_compactions: -1
2025/06/17-01:33:32.470904 734c             Options.max_subcompactions: 1
2025/06/17-01:33:32.470906 734c             Options.avoid_flush_during_shutdown: 0
2025/06/17-01:33:32.470908 734c           Options.writable_file_max_buffer_size: 1048576
2025/06/17-01:33:32.470911 734c             Options.delayed_write_rate : 16777216
2025/06/17-01:33:32.470913 734c             Options.max_total_wal_size: 0
2025/06/17-01:33:32.470915 734c             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/17-01:33:32.470917 734c                   Options.stats_dump_period_sec: 600
2025/06/17-01:33:32.470919 734c                 Options.stats_persist_period_sec: 600
2025/06/17-01:33:32.470921 734c                 Options.stats_history_buffer_size: 1048576
2025/06/17-01:33:32.470924 734c                          Options.max_open_files: -1
2025/06/17-01:33:32.470926 734c                          Options.bytes_per_sync: 0
2025/06/17-01:33:32.470928 734c                      Options.wal_bytes_per_sync: 0
2025/06/17-01:33:32.470930 734c                   Options.strict_bytes_per_sync: 0
2025/06/17-01:33:32.470932 734c       Options.compaction_readahead_size: 0
2025/06/17-01:33:32.470934 734c                  Options.max_background_flushes: -1
2025/06/17-01:33:32.470936 734c Compression algorithms supported:
2025/06/17-01:33:32.471145 734c 	kZSTD supported: 0
2025/06/17-01:33:32.471151 734c 	kSnappyCompression supported: 0
2025/06/17-01:33:32.471154 734c 	kBZip2Compression supported: 0
2025/06/17-01:33:32.471157 734c 	kZlibCompression supported: 1
2025/06/17-01:33:32.471160 734c 	kLZ4Compression supported: 1
2025/06/17-01:33:32.471163 734c 	kXpressCompression supported: 0
2025/06/17-01:33:32.471166 734c 	kLZ4HCCompression supported: 1
2025/06/17-01:33:32.471169 734c 	kZSTDNotFinalCompression supported: 0
2025/06/17-01:33:32.471210 734c Fast CRC32 supported: Not supported on x86
2025/06/17-01:33:32.471215 734c DMutex implementation: std::mutex
2025/06/17-01:33:32.472763 734c [db\version_set.cc:5791] Recovering from manifest file: G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/MANIFEST-005858
2025/06/17-01:33:32.474311 734c [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/17-01:33:32.474422 734c               Options.comparator: leveldb.BytewiseComparator
2025/06/17-01:33:32.474453 734c           Options.merge_operator: None
2025/06/17-01:33:32.474457 734c        Options.compaction_filter: None
2025/06/17-01:33:32.474461 734c        Options.compaction_filter_factory: None
2025/06/17-01:33:32.474465 734c  Options.sst_partitioner_factory: None
2025/06/17-01:33:32.474468 734c         Options.memtable_factory: SkipListFactory
2025/06/17-01:33:32.474472 734c            Options.table_factory: BlockBasedTable
2025/06/17-01:33:32.474527 734c            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001F501D37E40)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001F509F10CA0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/17-01:33:32.474543 734c        Options.write_buffer_size: 67108864
2025/06/17-01:33:32.474547 734c  Options.max_write_buffer_number: 2
2025/06/17-01:33:32.474549 734c          Options.compression: LZ4
2025/06/17-01:33:32.474552 734c                  Options.bottommost_compression: Disabled
2025/06/17-01:33:32.474555 734c       Options.prefix_extractor: nullptr
2025/06/17-01:33:32.474558 734c   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-01:33:32.474562 734c             Options.num_levels: 7
2025/06/17-01:33:32.474565 734c        Options.min_write_buffer_number_to_merge: 1
2025/06/17-01:33:32.474568 734c     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-01:33:32.474572 734c     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-01:33:32.474574 734c            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-01:33:32.474577 734c                  Options.bottommost_compression_opts.level: 32767
2025/06/17-01:33:32.474581 734c               Options.bottommost_compression_opts.strategy: 0
2025/06/17-01:33:32.474584 734c         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-01:33:32.474587 734c         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-01:33:32.474590 734c         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-01:33:32.474593 734c                  Options.bottommost_compression_opts.enabled: false
2025/06/17-01:33:32.474596 734c         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-01:33:32.474599 734c         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-01:33:32.474603 734c            Options.compression_opts.window_bits: -14
2025/06/17-01:33:32.474605 734c                  Options.compression_opts.level: 32767
2025/06/17-01:33:32.474607 734c               Options.compression_opts.strategy: 0
2025/06/17-01:33:32.474669 734c         Options.compression_opts.max_dict_bytes: 0
2025/06/17-01:33:32.474676 734c         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-01:33:32.474680 734c         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-01:33:32.474683 734c         Options.compression_opts.parallel_threads: 1
2025/06/17-01:33:32.474686 734c                  Options.compression_opts.enabled: false
2025/06/17-01:33:32.474690 734c         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-01:33:32.474693 734c      Options.level0_file_num_compaction_trigger: 4
2025/06/17-01:33:32.474696 734c          Options.level0_slowdown_writes_trigger: 20
2025/06/17-01:33:32.474700 734c              Options.level0_stop_writes_trigger: 36
2025/06/17-01:33:32.474703 734c                   Options.target_file_size_base: 67108864
2025/06/17-01:33:32.474721 734c             Options.target_file_size_multiplier: 1
2025/06/17-01:33:32.474755 734c                Options.max_bytes_for_level_base: 268435456
2025/06/17-01:33:32.474769 734c Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-01:33:32.474773 734c          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-01:33:32.474799 734c Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-01:33:32.474804 734c Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-01:33:32.474808 734c Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-01:33:32.474811 734c Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-01:33:32.474814 734c Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-01:33:32.474830 734c Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-01:33:32.474845 734c Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-01:33:32.474849 734c       Options.max_sequential_skip_in_iterations: 8
2025/06/17-01:33:32.474852 734c                    Options.max_compaction_bytes: 1677721600
2025/06/17-01:33:32.474855 734c   Options.ignore_max_compaction_bytes_for_input: true
2025/06/17-01:33:32.474858 734c                        Options.arena_block_size: 1048576
2025/06/17-01:33:32.474861 734c   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-01:33:32.474864 734c   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-01:33:32.474867 734c                Options.disable_auto_compactions: 0
2025/06/17-01:33:32.474872 734c                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-01:33:32.474876 734c                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-01:33:32.474880 734c Options.compaction_options_universal.size_ratio: 1
2025/06/17-01:33:32.474884 734c Options.compaction_options_universal.min_merge_width: 2
2025/06/17-01:33:32.474887 734c Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-01:33:32.474890 734c Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-01:33:32.474893 734c Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-01:33:32.474897 734c Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-01:33:32.474899 734c Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-01:33:32.474902 734c Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-01:33:32.474909 734c                   Options.table_properties_collectors: 
2025/06/17-01:33:32.474929 734c                   Options.inplace_update_support: 0
2025/06/17-01:33:32.474936 734c                 Options.inplace_update_num_locks: 10000
2025/06/17-01:33:32.474940 734c               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-01:33:32.474944 734c               Options.memtable_whole_key_filtering: 0
2025/06/17-01:33:32.474961 734c   Options.memtable_huge_page_size: 0
2025/06/17-01:33:32.474969 734c                           Options.bloom_locality: 0
2025/06/17-01:33:32.474973 734c                    Options.max_successive_merges: 0
2025/06/17-01:33:32.474980 734c                Options.optimize_filters_for_hits: 0
2025/06/17-01:33:32.474985 734c                Options.paranoid_file_checks: 0
2025/06/17-01:33:32.474988 734c                Options.force_consistency_checks: 1
2025/06/17-01:33:32.474992 734c                Options.report_bg_io_stats: 0
2025/06/17-01:33:32.474995 734c                               Options.ttl: 2592000
2025/06/17-01:33:32.474998 734c          Options.periodic_compaction_seconds: 0
2025/06/17-01:33:32.475001 734c  Options.preclude_last_level_data_seconds: 0
2025/06/17-01:33:32.475004 734c    Options.preserve_internal_time_seconds: 0
2025/06/17-01:33:32.475007 734c                       Options.enable_blob_files: false
2025/06/17-01:33:32.475011 734c                           Options.min_blob_size: 0
2025/06/17-01:33:32.475014 734c                          Options.blob_file_size: 268435456
2025/06/17-01:33:32.475062 734c                   Options.blob_compression_type: NoCompression
2025/06/17-01:33:32.475131 734c          Options.enable_blob_garbage_collection: false
2025/06/17-01:33:32.475137 734c      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-01:33:32.475143 734c Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-01:33:32.475147 734c          Options.blob_compaction_readahead_size: 0
2025/06/17-01:33:32.475150 734c                Options.blob_file_starting_level: 0
2025/06/17-01:33:32.475154 734c Options.experimental_mempurge_threshold: 0.000000
2025/06/17-01:33:32.480133 734c [db\version_set.cc:5842] Recovered from manifest file:G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/MANIFEST-005858 succeeded,manifest_file_number is 5858, next_file_number is 5860, last_sequence is 573000, log_number is 5853,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 5853
2025/06/17-01:33:32.480158 734c [db\version_set.cc:5851] Column family [default] (ID 0), log number is 5853
2025/06/17-01:33:32.480734 734c [db\db_impl\db_impl_open.cc:636] DB ID: 1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc
2025/06/17-01:33:32.483354 734c EVENT_LOG_v1 {"time_micros": 1750113212483344, "job": 1, "event": "recovery_started", "wal_files": [5857]}
2025/06/17-01:33:32.483381 734c [db\db_impl\db_impl_open.cc:1131] Recovering log #5857 mode 2
2025/06/17-01:33:32.497726 734c EVENT_LOG_v1 {"time_micros": 1750113212497682, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 5861, "file_size": 1275, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 573001, "largest_seqno": 573008, "table_properties": {"data_size": 240, "index_size": 76, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 608, "raw_average_key_size": 76, "raw_value_size": 44, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 8, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750113212, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ffa8411-b1ed-11ef-9c7f-e778ab55c9bc", "db_session_id": "MEFMS7RH377QPMTVS7V0", "orig_file_number": 5861, "seqno_to_time_mapping": "N/A"}}
2025/06/17-01:33:32.498757 734c EVENT_LOG_v1 {"time_micros": 1750113212498749, "job": 1, "event": "recovery_finished"}
2025/06/17-01:33:32.499384 734c [db\version_set.cc:5304] Creating manifest 5863
2025/06/17-01:33:32.521184 734c [file\delete_scheduler.cc:77] Deleted file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default/005857.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/17-01:33:32.521212 734c [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file G:\[NajmClean]\Zahyanjm\ZahyaV1\server-data\db\default//LOG.old.1750007645279888
2025/06/17-01:33:32.521910 734c [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 000001F509B499B0
2025/06/17-01:33:32.522102 7580 (Original Log Time 2025/06/17-01:33:32.521996) [db\db_impl\db_impl_compaction_flush.cc:3398] Compaction nothing to do
2025/06/17-01:33:32.522778 734c DB pointer 000001F509FE0A40
2025/06/17-01:33:32.524252 d858 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/17-01:33:32.524275 d858 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.74 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.014       0      0       0.0       0.0
  L1      1/0   739.79 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0   743.53 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.014       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.014       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.014       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001F509F10CA0#27148 capacity: 32.00 MB seed: 622122374 usage: 0.71 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.000419 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.54 KB,0.00163913%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
