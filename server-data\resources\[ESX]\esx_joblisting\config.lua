Config              = {}

Config.DrawDistance = 100.0
Config.ZoneSize     = {x = 1.5, y = 1.5, z = 1.0}
Config.MarkerColor  = {r = 100, g = 100, b = 204}
Config.MarkerType   = 22

Config.Locale       = 'en'

Config.Zones = {
	vector3(-1083.08, -248.72, 37.76)
}

-- نظام متطلبات الخبرة للوظائف
Config.ExperienceSystem = {
    Enabled = true, -- تفعيل/إلغاء تفعيل نظام الخبرة

    -- الألوان المستخدمة في القائمة
    Colors = {
        Eligible = '#00ff00',    -- أخضر للمؤهل
        NotEligible = '#ff0000', -- أحمر لغير المؤهل
        Default = '#ffffff'      -- أبيض افتراضي
    },

    -- رسائل النظام
    Messages = {
        NotEligible = 'تحتاج إلى مستوى خبرة %s للتقديم على هذه الوظيفة. مستواك الحالي: %s',
        CurrentLevel = 'مستوى خبرتك الحالي: %s'
    },

    -- متطلبات الخبرة للوظائف المحددة فقط (الوظائف غير المذكورة هنا ستظهر بدون متطلبات خبرة)
    JobRequirements = {
        ['taxi'] = 5,
        ['mechanic'] = 10,
        ['ambulance'] = 15,
        ['police'] = 20,
        ['agent'] = 25,
        ['admin'] = 50
    }
}
