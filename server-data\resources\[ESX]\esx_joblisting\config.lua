Config              = {}

Config.DrawDistance = 100.0
Config.ZoneSize     = {x = 1.5, y = 1.5, z = 1.0}
Config.MarkerColor  = {r = 100, g = 100, b = 204}
Config.MarkerType   = 22

Config.Locale       = 'en'

Config.Zones = {
	vector3(-1083.08, -248.72, 37.76)
}

-- نظام متطلبات الخبرة للوظائف
Config.ExperienceSystem = {
    Enabled = true, -- تفعيل/إلغاء تفعيل نظام الخبرة

    -- الألوان المستخدمة في القائمة
    Colors = {
        Eligible = '#00ff00',    -- أخضر للمؤهل
        NotEligible = '#ff0000', -- أحمر لغير المؤهل
        Default = '#ffffff'      -- أبيض افتراضي
    },

    -- رسائل النظام
    Messages = {
        NotEligible = 'تحتاج إلى مستوى خبرة %s للتقديم على هذه الوظيفة. مستواك الحالي: %s',
        CurrentLevel = 'مستوى خبرتك الحالي: %s'
    },

    -- متطلبات الخبرة للوظائف (يمكن تعديلها هنا بدلاً من قاعدة البيانات)
    JobRequirements = {
        ['unemployed'] = 0,
        ['taxi'] = 500,
         ['farmer'] = 0,
		['fisherman'] =0,
		 ['fueler'] = 0,
		['lumberjack'] = 0,
		['miner'] = 0,
		['OilRig'] = 0,
		['slaughterer'] = 0,
		['tailor'] = 0, 
		 
		 
    }
}
