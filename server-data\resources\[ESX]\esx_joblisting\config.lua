Config              = {}

Config.DrawDistance = 100.0
Config.ZoneSize     = {x = 1.5, y = 1.5, z = 1.0}
Config.MarkerColor  = {r = 100, g = 100, b = 204}
Config.MarkerType   = 22

Config.Locale       = 'en'

Config.Zones = {
	vector3(-1083.08, -248.72, 37.76)
}

-- نظام متطلبات الخبرة للوظائف
Config.ExperienceSystem = {
    Enabled = true, -- تفعيل/إلغاء تفعيل نظام الخبرة

    -- الألوان المستخدمة في القائمة
    Colors = {
        Eligible = '#00ff00',    -- أخضر للمؤهل
        NotEligible = '#ff0000', -- أحمر لغير المؤهل
        Default = '#ffffff'      -- أبيض افتراضي
    },

    -- رسائل النظام
Messages = {
    NotEligible = '<span style="color:#ff0000;">تحتاج إلى مستوى خبرة <span style="color:#ffff00;">%d</span> للتقديم على هذه الوظيفة.</span><br><span style="color:#00ff00;">مستواك الحالي: %d</span>',
    CurrentLevel = 'مستوى خبرتك الحالي: <span style="color:#2ecc71;">%s</span>'
},

    -- متطلبات الخبرة للوظائف (يمكن تعديلها هنا بدلاً من قاعدة البيانات)
    JobRequirements = {
        ['unemployed'] = 0,
        ['taxi'] = 500,
         ['farmer'] = 0,
		['fisherman'] =0,
		 ['fueler'] = 0,
		['lumberjack'] = 0,
		['miner'] = 0,
		['OilRig'] = 0,
		['slaughterer'] = 0,
		['tailor'] = 0, 
		 
		 
    }
}
