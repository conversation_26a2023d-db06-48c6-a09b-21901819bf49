-- إضا<PERSON>ة عمود required_level لجدول jobs
-- هذا العمود سيحدد مستوى الخبرة المطلوب لكل وظيفة

ALTER TABLE `jobs` ADD COLUMN `required_level` INT DEFAULT 0 COMMENT 'مستوى الخبرة المطلوب للوظيفة';

-- تحديث الوظائف الموجودة بمتطلبات الخبرة
UPDATE `jobs` SET `required_level` = 0 WHERE `name` = 'unemployed';
UPDATE `jobs` SET `required_level` = 5 WHERE `name` = 'taxi';
UPDATE `jobs` SET `required_level` = 10 WHERE `name` = 'mechanic';
UPDATE `jobs` SET `required_level` = 15 WHERE `name` = 'ambulance';
UPDATE `jobs` SET `required_level` = 20 WHERE `name` = 'police';
UPDATE `jobs` SET `required_level` = 25 WHERE `name` = 'agent';
UPDATE `jobs` SET `required_level` = 50 WHERE `name` = 'admin';

-- يمكنك تشغيل هذا الملف في قاعدة البيانات لإضافة نظام الخبرة
-- أو يمكنك استخدام الإعدادات في config.lua بدلاً من ذلك
